import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.callbacks import EarlyStopping
from tensorflow.keras.optimizers import Adam

print("TensorFlow version:", tf.__version__)
print("Keras version:", tf.keras.__version__)

# 创建一个简单的LSTM模型
model = Sequential([
    LSTM(10, activation='relu', input_shape=(5, 3)),
    Dense(1)
])

# 编译模型
model.compile(optimizer=Adam(learning_rate=0.001), loss='mse')

print("模型创建成功!")
print(model.summary())
