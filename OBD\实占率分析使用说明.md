# 实占率分析工具使用说明

## 功能概述

这个工具用于分析分光器实占率变化趋势，为不同变化类型的设备选择合适的预测模型。

## 主要功能

### 1. 数据加载和预处理
- 自动读取指定路径下包含"三网小区预警"的xlsx文件
- 支持时间范围过滤（2024年9月27日到2025年4月22日）
- 自动计算实占率、潜在需求比等关键指标

### 2. 设备分类
根据以下维度对设备进行分类：

#### 数据充足性
- **数据充足**：≥8个数据点，支持机器学习模型
- **数据不足**：<8个数据点，只能使用简单统计方法

#### 变化趋势类型
- **稳定型**：变异系数<0.05，实占率变化很小
- **正常型**：变异系数在0.05-0.15之间，有一定变化但相对稳定
- **波动型**：变异系数>0.15，变化较大
- **扩容型**：检测到容量增加或实占率突降

#### 扩容状态
- **无扩容**：没有检测到扩容迹象
- **近期扩容**：最近12周内有扩容
- **历史扩容**：历史上有过扩容

#### 稳定性类型
- **长期稳定**：入网时间≥24个月
- **新设备**：入网时间<24个月

### 3. 模型推荐
根据设备分类自动推荐最适合的预测模型：

- **LSTM神经网络**：数据充足的波动型设备
- **SARIMA时间序列模型**：数据充足的正常变化设备
- **指数平滑**：扩容设备或数据不足的波动设备
- **移动平均**：稳定设备
- **线性回归**：数据不足的正常设备
- **简单平均**：数据严重不足的设备

### 4. 可视化分析
生成多种图表：
- 设备分类分布饼图
- 实占率统计分布直方图
- 变异系数分布图
- 趋势斜率分布图

### 5. 结果导出
- Excel格式的详细分类结果
- 统计汇总表
- 可视化图表保存

## 使用方法

### 基本使用
```python
from occupancy_rate_analysis import OccupancyRateAnalyzer

# 创建分析器
analyzer = OccupancyRateAnalyzer(data_path="D:/OBD/data_processed")

# 运行完整分析
analyzer.run_complete_analysis()
```

### 自定义参数
```python
# 调整分类阈值
analyzer.thresholds.update({
    'stable_cv': 0.05,      # 稳定设备变异系数阈值
    'volatile_cv': 0.15,    # 波动设备变异系数阈值
    'min_data_points': 8    # 最少数据点数
})

# 运行分析
analyzer.run_complete_analysis(
    save_visualizations=True,  # 是否保存图表
    export_results=True        # 是否导出Excel
)
```

### 分步执行
```python
# 1. 加载数据
analyzer.load_data()

# 2. 设备分类
analyzer.classify_devices()

# 3. 生成报告
analyzer.generate_analysis_report()

# 4. 创建可视化
analyzer.create_visualizations()

# 5. 导出结果
analyzer.export_results()
```

## 输出文件

### 1. 可视化图表
- `./analysis_results/设备分类分析.png`
- `./analysis_results/实占率统计分析.png`

### 2. Excel结果文件
- `./analysis_results/device_classification_results.xlsx`
  - Sheet1: 设备分类结果（详细信息）
  - Sheet2: 统计汇总

## 分类阈值说明

可以根据实际需求调整以下阈值：

```python
thresholds = {
    'min_data_points': 8,           # 最少数据点数（支持机器学习）
    'stable_cv': 0.05,              # 稳定设备变异系数阈值
    'volatile_cv': 0.15,            # 波动设备变异系数阈值
    'expansion_capacity_ratio': 0.1, # 扩容容量变化比例
    'expansion_occupancy_drop': 0.15, # 扩容实占率下降阈值
    'long_term_months': 24,         # 长期稳定设备入网时间阈值（月）
    'recent_expansion_weeks': 12    # 近期扩容检查周数
}
```

## 注意事项

1. **数据文件要求**：
   - 文件名包含"三网小区预警"
   - 格式为xlsx
   - 数据在Sheet1中

2. **必需的数据列**：
   - 所属设备
   - 分光器容量
   - 分光器空闲数
   - ftth终端数
   - 覆盖的工程级的线路到达房间数
   - 小区入库时间

3. **时间处理**：
   - 如果数据中没有'now'时间列，工具会尝试从其他信息推断
   - 建议确保数据中包含明确的时间信息

4. **结果解读**：
   - 变异系数越小，设备越稳定
   - 扩容设备需要特别关注，可能影响预测准确性
   - 数据不足的设备建议增加观察期

## 扩展功能

如需添加新的分类逻辑或模型推荐，可以修改以下方法：
- `classify_single_device()`: 设备分类逻辑
- `detect_expansion()`: 扩容检测逻辑
- `print_model_recommendations()`: 模型推荐逻辑
