import os
import pandas as pd
import warnings
from pmdarima import auto_arima
from statsmodels.tsa.statespace.sarimax import SARIMAX
from sklearn.metrics import mean_squared_error
from sklearn.model_selection import train_test_split


# 1. 数据加载与预处理
def load_and_preprocess_data(folder_path):
    # 读取所有以'三网小区预警_'开头的Excel文件
    excel_files = [f for f in os.listdir(folder_path)
                   if f.startswith('三网小区预警_') and f.endswith('.xlsx')]
    all_data = pd.DataFrame()

    for file in excel_files:
        file_path = os.path.join(folder_path, file)
        df = pd.read_excel(file_path)
        # 筛选关键列并转换时间格式
        df = df[[
            '所属设备', '小区入库时间', '覆盖的工程级的线路到达房间数',
            '分光器容量', '分光器空闲数', 'ftth终端数', 'now', '实占率'
        ]]
        df['now'] = pd.to_datetime(df['now'], errors='coerce')  # 添加errors='coerce'处理异常值
        df['小区入库时间'] = pd.to_datetime(df['小区入库时间'], errors='coerce')
        all_data = pd.concat([all_data, df], ignore_index=True)
    return all_data

# 2. 特征工程
def feature_engineering(all_data):
    # 计算动态特征
    all_data['空闲率'] = all_data['分光器空闲数'] / all_data['分光器容量']
    all_data['潜在需求比'] = all_data['ftth终端数'] / (all_data['覆盖的工程级的线路到达房间数'])
    all_data['分光器利用率'] = 1 - all_data['空闲率']

    # 计算时间差特征（入库时间与预测时间的间隔）
    time_diff = (all_data['now'] - all_data['小区入库时间'])
    if time_diff.isnull().any():
        print("警告：存在无效的时间差计算，已自动填充为0")
        time_diff = time_diff.fillna(pd.Timedelta(days=0))

    all_data['入库时间差_月'] = time_diff.dt.days / 30
    return all_data

# 3. 按设备ID和周聚合数据
def aggregate_weekly_data(all_data):
    device_data = all_data[[
        '所属设备', 'now', '实占率', '潜在需求比',
        '入库时间差_月', '分光器利用率', '分光器容量'
    ]].copy()
    # 按周分桶
    device_data['周'] = device_data['now'].dt.to_period('W').dt.start_time
    # 按设备和周聚合（静态字段取首次出现值）
    weekly_data = device_data.groupby(['所属设备', '周']).agg({
        '实占率': 'mean',
        '潜在需求比': 'mean',
        '入库时间差_月': 'mean',
        '分光器利用率': 'mean',
        '分光器容量': 'first'
    }).reset_index()
    return weekly_data

# 4. 划分训练集与测试集
def split_train_test(weekly_data):
    # 按时间顺序划分，避免数据泄漏
    train_data, test_data = train_test_split(
        weekly_data, test_size=0.2, shuffle=False
    )
    return train_data, test_data

# 5. 训练模型与预测
def train_and_predict(train_data, test_data):
    predictions = {}
    validation_results = []
    forecast_weeks = 1  # 预测未来1周

    for device in weekly_data['所属设备'].unique():
        device_data = weekly_data[weekly_data['所属设备'] == device].sort_values('周')
        if len(device_data) < 10:  # 忽略数据不足的设备
            continue

        split_idx = int(len(device_data) * 0.8)
        device_train_data = device_data.iloc[:split_idx]
        device_test_data = device_data.iloc[split_idx:]

        if len(device_train_data) < 6 or len(device_test_data) == 0:
            continue

        # 外生变量（动态特征 + 静态特征）
        exog_train = device_train_data[['潜在需求比', '入库时间差_月', '分光器利用率', '分光器容量']]

        # 自动选择最优SARIMAX参数
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            model = auto_arima(
                device_train_data['实占率'],
                exogenous=exog_train,
                seasonal=False,
                trace=False,
                error_action='ignore'
            )

        model_fit = model.fit(device_train_data['实占率'], exogenous=exog_train)

        # 逐步滚动预测
        test_pred = []
        exog_test = device_test_data[[
            '潜在需求比', '入库时间差_月',
            '分光器利用率', '分光器容量'
        ]]

        for t in range(len(device_test_data)):
            current_exog = exog_test.iloc[t:t + 1]
            pred = model_fit.predict(n_periods=1, exogenous=current_exog)
            test_pred.append(pred.iloc[0])

        # 记录结果
        mse = mean_squared_error(device_test_data['实占率'], test_pred)
        predictions[device] = test_pred[-forecast_weeks:]
        validation_results.append({
            '设备': device,
            'MSE': mse,
            '实际': device_test_data['实占率'].values[-1],
            '预测': test_pred[-1]
        })
    return predictions, validation_results

# 6. 输出结果与保存
def save_results(validation_results, predictions):
    validation_df = pd.DataFrame(validation_results)
    validation_df.to_excel("模型验证结果.xlsx", index=False)
    print("验证结果：\n", validation_df)

    output = []
    for device, forecast in predictions.items():
        prediction_row = {
            '设备': device,
            '预测实占率': forecast[0],
            '提醒': '正常' if forecast[0] < 0.95 else '需扩容'  # 阈值设为0.95
        }
        output.append(prediction_row)

    output_df = pd.DataFrame(output)
    output_file = '设备实占率预测结果.xlsx'
    output_df.to_excel(output_file, index=False)
    print(f'预测结果已保存至 {output_file}')

# 主流程
if __name__ == '__main__':
    folder_path = 'D:/OBD/data_processed'  # 输入文件夹路径

    # 执行所有步骤
    all_data = load_and_preprocess_data(folder_path)
    print("step1 done")
    all_data = feature_engineering(all_data)
    print("step2 done")
    weekly_data = aggregate_weekly_data(all_data)
    print("step3 done")
    train_data, test_data = split_train_test(weekly_data)
    print("step4 done")
    predictions, validation_results = train_and_predict(train_data, test_data)
    print("step5 done")
    save_results(validation_results, predictions)
    print("step6 done")