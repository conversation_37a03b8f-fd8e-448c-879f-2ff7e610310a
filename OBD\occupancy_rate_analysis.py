#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实占率分析工具
用于分析分光器实占率变化趋势，为不同变化类型的设备选择合适的预测模型

作者：AI Assistant
创建时间：2025-06-17
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import glob
import warnings
from datetime import datetime, timedelta
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
import matplotlib.dates as mdates
from scipy import stats
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

warnings.filterwarnings('ignore')

class OccupancyRateAnalyzer:
    """实占率分析器"""
    
    def __init__(self, data_path="D:/OBD/data_processed"):
        self.data_path = data_path
        self.all_data = None
        self.device_classifications = {}
        self.analysis_results = {}
        
        # 分析时间范围
        self.start_date = datetime(2024, 9, 27)
        self.end_date = datetime(2025, 4, 22)
        
        # 分类阈值
        self.thresholds = {
            'min_data_points': 8,  # 最少数据点数（支持机器学习）
            'stable_cv': 0.05,     # 稳定设备变异系数阈值
            'volatile_cv': 0.15,   # 波动设备变异系数阈值
            'expansion_capacity_ratio': 0.1,  # 扩容容量变化比例
            'expansion_occupancy_drop': 0.15,  # 扩容实占率下降阈值
            'long_term_months': 24,  # 长期稳定设备入网时间阈值（月）
            'recent_expansion_weeks': 12  # 近期扩容检查周数
        }
    
    def load_data(self):
        """加载数据文件"""
        print("开始加载数据文件...")
        
        # 查找包含"三网小区预警"的xlsx文件
        pattern = os.path.join(self.data_path, "*三网小区预警*.xlsx")
        files = glob.glob(pattern)
        
        if not files:
            raise FileNotFoundError(f"在路径 {self.data_path} 下未找到包含'三网小区预警'的xlsx文件")
        
        print(f"找到 {len(files)} 个数据文件")
        
        all_dataframes = []
        
        for file_path in files:
            try:
                print(f"正在读取: {os.path.basename(file_path)}")
                df = pd.read_excel(file_path, sheet_name='Sheet1')
                
                # 添加文件信息
                df['数据文件'] = os.path.basename(file_path)
                all_dataframes.append(df)
                
            except Exception as e:
                print(f"读取文件 {file_path} 失败: {e}")
                continue
        
        if not all_dataframes:
            raise ValueError("没有成功读取任何数据文件")
        
        # 合并所有数据
        self.all_data = pd.concat(all_dataframes, ignore_index=True)
        print(f"总共加载 {len(self.all_data)} 条记录")
        
        return self.preprocess_data()
    
    def preprocess_data(self):
        """数据预处理"""
        print("开始数据预处理...")
        
        # 检查必要的列
        required_columns = ['所属设备', '分光器容量', '分光器空闲数', 'ftth终端数', 
                          '覆盖的工程级的线路到达房间数', '小区入库时间']
        
        missing_columns = [col for col in required_columns if col not in self.all_data.columns]
        if missing_columns:
            print(f"警告：缺少以下列: {missing_columns}")
        
        # 处理时间列
        if 'now' not in self.all_data.columns:
            # 尝试从文件名或其他列推断时间
            print("警告：未找到'now'时间列，尝试从其他信息推断...")
            # 这里可以根据实际数据结构调整
        
        # 计算基本特征
        self.all_data = self.feature_engineering(self.all_data)
        
        # 过滤时间范围
        if 'now' in self.all_data.columns:
            self.all_data = self.all_data[
                (self.all_data['now'] >= self.start_date) & 
                (self.all_data['now'] <= self.end_date)
            ]
            print(f"时间过滤后剩余 {len(self.all_data)} 条记录")
        
        return self.all_data
    
    def feature_engineering(self, data):
        """特征工程"""
        print("进行特征工程...")
        
        # 基本特征计算
        data['空闲率'] = data['分光器空闲数'] / data['分光器容量']
        data['实占率'] = 1 - data['空闲率']
        data['潜在需求比'] = data['ftth终端数'] / data['覆盖的工程级的线路到达房间数']
        data['空闲端口数'] = data['分光器空闲数']
        
        # 处理时间相关特征
        if '小区入库时间' in data.columns:
            data['小区入库时间'] = pd.to_datetime(data['小区入库时间'], errors='coerce')
            if 'now' in data.columns:
                data['入库时间差_月'] = ((data['now'] - data['小区入库时间']).dt.days / 30).round()
            else:
                # 如果没有now列，使用当前时间
                data['入库时间差_月'] = ((datetime.now() - data['小区入库时间']).dt.days / 30).round()
        
        # 按设备排序
        data = data.sort_values(['所属设备', 'now'] if 'now' in data.columns else ['所属设备'])
        
        # 计算变化特征
        data['容量变化'] = data.groupby('所属设备')['分光器容量'].diff().fillna(0)
        data['实占率变化'] = data.groupby('所属设备')['实占率'].diff().fillna(0)
        data['空闲端口数变化'] = data.groupby('所属设备')['空闲端口数'].diff().fillna(0)
        
        return data
    
    def classify_devices(self):
        """设备分类主函数"""
        print("开始设备分类...")
        
        device_groups = self.all_data.groupby('所属设备')
        classifications = {}
        
        for device_id, device_data in device_groups:
            classification = self.classify_single_device(device_id, device_data)
            classifications[device_id] = classification
        
        self.device_classifications = classifications
        return classifications
    
    def classify_single_device(self, device_id, device_data):
        """单个设备分类"""
        device_data = device_data.sort_values('now' if 'now' in device_data.columns else device_data.index)
        
        classification = {
            'device_id': device_id,
            'data_points': len(device_data),
            'data_sufficiency': 'sufficient' if len(device_data) >= self.thresholds['min_data_points'] else 'insufficient',
            'occupancy_stats': {},
            'trend_type': 'unknown',
            'expansion_status': 'none',
            'stability_type': 'unknown',
            'recommended_model': 'linear_regression',
            'analysis_notes': []
        }
        
        # 实占率统计
        occupancy_rates = device_data['实占率'].dropna()
        if len(occupancy_rates) > 0:
            classification['occupancy_stats'] = {
                'mean': occupancy_rates.mean(),
                'std': occupancy_rates.std(),
                'cv': occupancy_rates.std() / occupancy_rates.mean() if occupancy_rates.mean() > 0 else 0,
                'min': occupancy_rates.min(),
                'max': occupancy_rates.max(),
                'range': occupancy_rates.max() - occupancy_rates.min(),
                'trend_slope': self.calculate_trend_slope(occupancy_rates)
            }
        
        # 数据充足性分析
        if classification['data_sufficiency'] == 'insufficient':
            classification['recommended_model'] = 'simple_average'
            classification['analysis_notes'].append(f"数据点不足({len(device_data)}个)，建议使用简单平均或最近值预测")
        
        # 扩容检测
        classification['expansion_status'] = self.detect_expansion(device_data)
        
        # 趋势分类
        if len(occupancy_rates) >= 3:
            cv = classification['occupancy_stats']['cv']
            
            if classification['expansion_status'] != 'none':
                classification['trend_type'] = 'expanded'
                classification['recommended_model'] = 'exponential_smoothing'
            elif cv < self.thresholds['stable_cv']:
                classification['trend_type'] = 'stable'
                classification['recommended_model'] = 'moving_average'
            elif cv > self.thresholds['volatile_cv']:
                classification['trend_type'] = 'volatile'
                classification['recommended_model'] = 'lstm' if classification['data_sufficiency'] == 'sufficient' else 'exponential_smoothing'
            else:
                classification['trend_type'] = 'normal'
                classification['recommended_model'] = 'sarima' if classification['data_sufficiency'] == 'sufficient' else 'linear_regression'
        
        # 稳定性分析
        if '入库时间差_月' in device_data.columns:
            avg_months = device_data['入库时间差_月'].mean()
            if avg_months >= self.thresholds['long_term_months']:
                classification['stability_type'] = 'long_term_stable'
                if classification['trend_type'] == 'stable':
                    classification['analysis_notes'].append("长期稳定设备，实占率变化很小")
            else:
                classification['stability_type'] = 'new_device'
                classification['analysis_notes'].append("新设备，需要更多观察期")
        
        return classification

    def detect_expansion(self, device_data):
        """检测扩容情况"""
        # 检查容量变化
        capacity_changes = device_data['容量变化'].sum()
        initial_capacity = device_data['分光器容量'].iloc[0] if len(device_data) > 0 else 1
        capacity_ratio = capacity_changes / initial_capacity if initial_capacity > 0 else 0

        # 检查实占率突降
        occupancy_changes = device_data['实占率变化']
        significant_drops = (occupancy_changes < -self.thresholds['expansion_occupancy_drop']).sum()

        # 检查近期扩容
        recent_weeks = min(self.thresholds['recent_expansion_weeks'], len(device_data))
        if recent_weeks > 0:
            recent_data = device_data.iloc[-recent_weeks:]
            recent_capacity_change = recent_data['容量变化'].sum()
            recent_capacity_ratio = recent_capacity_change / initial_capacity if initial_capacity > 0 else 0

            if recent_capacity_ratio > self.thresholds['expansion_capacity_ratio']:
                return 'recent_expansion'

        if capacity_ratio > self.thresholds['expansion_capacity_ratio'] or significant_drops > 0:
            return 'historical_expansion'

        return 'none'

    def calculate_trend_slope(self, values):
        """计算趋势斜率"""
        if len(values) < 3:
            return 0

        x = np.arange(len(values))
        try:
            slope, _, _, _, _ = stats.linregress(x, values)
            return slope
        except:
            return 0

    def generate_analysis_report(self):
        """生成分析报告"""
        if not self.device_classifications:
            print("请先运行设备分类")
            return

        print("\n" + "="*60)
        print("实占率分析报告")
        print("="*60)

        # 统计各类设备数量
        stats_summary = {
            'total_devices': len(self.device_classifications),
            'data_sufficient': 0,
            'data_insufficient': 0,
            'trend_types': {'stable': 0, 'normal': 0, 'volatile': 0, 'expanded': 0, 'unknown': 0},
            'expansion_status': {'none': 0, 'recent_expansion': 0, 'historical_expansion': 0},
            'stability_types': {'long_term_stable': 0, 'new_device': 0, 'unknown': 0},
            'recommended_models': {}
        }

        for device_id, classification in self.device_classifications.items():
            # 数据充足性统计
            if classification['data_sufficiency'] == 'sufficient':
                stats_summary['data_sufficient'] += 1
            else:
                stats_summary['data_insufficient'] += 1

            # 趋势类型统计
            trend_type = classification['trend_type']
            if trend_type in stats_summary['trend_types']:
                stats_summary['trend_types'][trend_type] += 1

            # 扩容状态统计
            expansion_status = classification['expansion_status']
            if expansion_status in stats_summary['expansion_status']:
                stats_summary['expansion_status'][expansion_status] += 1

            # 稳定性类型统计
            stability_type = classification['stability_type']
            if stability_type in stats_summary['stability_types']:
                stats_summary['stability_types'][stability_type] += 1

            # 推荐模型统计
            model = classification['recommended_model']
            stats_summary['recommended_models'][model] = stats_summary['recommended_models'].get(model, 0) + 1

        # 打印统计结果
        print(f"\n1. 总体统计:")
        print(f"   总设备数: {stats_summary['total_devices']}")
        print(f"   数据充足设备: {stats_summary['data_sufficient']} ({stats_summary['data_sufficient']/stats_summary['total_devices']*100:.1f}%)")
        print(f"   数据不足设备: {stats_summary['data_insufficient']} ({stats_summary['data_insufficient']/stats_summary['total_devices']*100:.1f}%)")

        print(f"\n2. 变化趋势分类:")
        for trend_type, count in stats_summary['trend_types'].items():
            percentage = count / stats_summary['total_devices'] * 100
            print(f"   {trend_type}: {count} ({percentage:.1f}%)")

        print(f"\n3. 扩容状态分析:")
        for expansion_status, count in stats_summary['expansion_status'].items():
            percentage = count / stats_summary['total_devices'] * 100
            print(f"   {expansion_status}: {count} ({percentage:.1f}%)")

        print(f"\n4. 稳定性分析:")
        for stability_type, count in stats_summary['stability_types'].items():
            percentage = count / stats_summary['total_devices'] * 100
            print(f"   {stability_type}: {count} ({percentage:.1f}%)")

        print(f"\n5. 推荐模型分布:")
        for model, count in stats_summary['recommended_models'].items():
            percentage = count / stats_summary['total_devices'] * 100
            print(f"   {model}: {count} ({percentage:.1f}%)")

        self.analysis_results = stats_summary
        return stats_summary

    def create_visualizations(self, save_path="./analysis_results"):
        """创建可视化图表"""
        if not self.device_classifications:
            print("请先运行设备分类")
            return

        # 创建保存目录
        os.makedirs(save_path, exist_ok=True)

        # 1. 设备分类饼图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('设备分类分析结果', fontsize=16, fontweight='bold')

        # 数据充足性分布
        data_sufficiency = [sum(1 for c in self.device_classifications.values() if c['data_sufficiency'] == 'sufficient'),
                           sum(1 for c in self.device_classifications.values() if c['data_sufficiency'] == 'insufficient')]
        axes[0,0].pie(data_sufficiency, labels=['数据充足', '数据不足'], autopct='%1.1f%%', startangle=90)
        axes[0,0].set_title('数据充足性分布')

        # 趋势类型分布
        trend_counts = {}
        for c in self.device_classifications.values():
            trend_type = c['trend_type']
            trend_counts[trend_type] = trend_counts.get(trend_type, 0) + 1

        if trend_counts:
            axes[0,1].pie(trend_counts.values(), labels=trend_counts.keys(), autopct='%1.1f%%', startangle=90)
            axes[0,1].set_title('变化趋势类型分布')

        # 扩容状态分布
        expansion_counts = {}
        for c in self.device_classifications.values():
            expansion_status = c['expansion_status']
            expansion_counts[expansion_status] = expansion_counts.get(expansion_status, 0) + 1

        if expansion_counts:
            axes[1,0].pie(expansion_counts.values(), labels=expansion_counts.keys(), autopct='%1.1f%%', startangle=90)
            axes[1,0].set_title('扩容状态分布')

        # 推荐模型分布
        model_counts = {}
        for c in self.device_classifications.values():
            model = c['recommended_model']
            model_counts[model] = model_counts.get(model, 0) + 1

        if model_counts:
            axes[1,1].pie(model_counts.values(), labels=model_counts.keys(), autopct='%1.1f%%', startangle=90)
            axes[1,1].set_title('推荐模型分布')

        plt.tight_layout()
        plt.savefig(os.path.join(save_path, '设备分类分析.png'), dpi=300, bbox_inches='tight')
        plt.show()

        # 2. 实占率统计分布图
        occupancy_stats = []
        for c in self.device_classifications.values():
            if c['occupancy_stats']:
                occupancy_stats.append(c['occupancy_stats'])

        if occupancy_stats:
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('实占率统计分析', fontsize=16, fontweight='bold')

            # 平均实占率分布
            means = [stats['mean'] for stats in occupancy_stats if 'mean' in stats]
            if means:
                axes[0,0].hist(means, bins=20, alpha=0.7, edgecolor='black')
                axes[0,0].set_title('平均实占率分布')
                axes[0,0].set_xlabel('实占率')
                axes[0,0].set_ylabel('设备数量')

            # 变异系数分布
            cvs = [stats['cv'] for stats in occupancy_stats if 'cv' in stats and not np.isnan(stats['cv'])]
            if cvs:
                axes[0,1].hist(cvs, bins=20, alpha=0.7, edgecolor='black')
                axes[0,1].set_title('变异系数分布')
                axes[0,1].set_xlabel('变异系数')
                axes[0,1].set_ylabel('设备数量')

            # 实占率范围分布
            ranges = [stats['range'] for stats in occupancy_stats if 'range' in stats]
            if ranges:
                axes[1,0].hist(ranges, bins=20, alpha=0.7, edgecolor='black')
                axes[1,0].set_title('实占率变化范围分布')
                axes[1,0].set_xlabel('变化范围')
                axes[1,0].set_ylabel('设备数量')

            # 趋势斜率分布
            slopes = [stats['trend_slope'] for stats in occupancy_stats if 'trend_slope' in stats and not np.isnan(stats['trend_slope'])]
            if slopes:
                axes[1,1].hist(slopes, bins=20, alpha=0.7, edgecolor='black')
                axes[1,1].set_title('趋势斜率分布')
                axes[1,1].set_xlabel('趋势斜率')
                axes[1,1].set_ylabel('设备数量')

            plt.tight_layout()
            plt.savefig(os.path.join(save_path, '实占率统计分析.png'), dpi=300, bbox_inches='tight')
            plt.show()

        print(f"可视化图表已保存到: {save_path}")

    def export_results(self, output_path="./analysis_results/device_classification_results.xlsx"):
        """导出分析结果"""
        if not self.device_classifications:
            print("请先运行设备分类")
            return

        # 创建保存目录
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # 准备导出数据
        export_data = []
        for device_id, classification in self.device_classifications.items():
            row = {
                '设备ID': device_id,
                '数据点数': classification['data_points'],
                '数据充足性': classification['data_sufficiency'],
                '趋势类型': classification['trend_type'],
                '扩容状态': classification['expansion_status'],
                '稳定性类型': classification['stability_type'],
                '推荐模型': classification['recommended_model'],
                '分析备注': '; '.join(classification['analysis_notes'])
            }

            # 添加实占率统计信息
            if classification['occupancy_stats']:
                stats = classification['occupancy_stats']
                row.update({
                    '平均实占率': round(stats.get('mean', 0), 4),
                    '实占率标准差': round(stats.get('std', 0), 4),
                    '变异系数': round(stats.get('cv', 0), 4),
                    '最小实占率': round(stats.get('min', 0), 4),
                    '最大实占率': round(stats.get('max', 0), 4),
                    '实占率范围': round(stats.get('range', 0), 4),
                    '趋势斜率': round(stats.get('trend_slope', 0), 6)
                })

            export_data.append(row)

        # 创建DataFrame并导出
        df_results = pd.DataFrame(export_data)

        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # 设备分类结果
            df_results.to_excel(writer, sheet_name='设备分类结果', index=False)

            # 统计汇总
            if self.analysis_results:
                summary_data = []
                for category, stats in self.analysis_results.items():
                    if isinstance(stats, dict):
                        for key, value in stats.items():
                            summary_data.append({
                                '分类类别': category,
                                '子类别': key,
                                '数量': value,
                                '占比(%)': round(value / self.analysis_results['total_devices'] * 100, 1) if self.analysis_results['total_devices'] > 0 else 0
                            })
                    else:
                        summary_data.append({
                            '分类类别': category,
                            '子类别': '-',
                            '数量': stats,
                            '占比(%)': 100 if category == 'total_devices' else 0
                        })

                df_summary = pd.DataFrame(summary_data)
                df_summary.to_excel(writer, sheet_name='统计汇总', index=False)

        print(f"分析结果已导出到: {output_path}")
        return output_path

    def run_complete_analysis(self, save_visualizations=True, export_results=True):
        """运行完整分析流程"""
        print("开始完整的实占率分析流程...")

        try:
            # 1. 加载数据
            self.load_data()

            # 2. 设备分类
            self.classify_devices()

            # 3. 生成报告
            self.generate_analysis_report()

            # 4. 创建可视化（可选）
            if save_visualizations:
                self.create_visualizations()

            # 5. 导出结果（可选）
            if export_results:
                self.export_results()

            print("\n分析完成！")

            # 6. 输出模型选择建议
            self.print_model_recommendations()

        except Exception as e:
            print(f"分析过程中出现错误: {e}")
            import traceback
            traceback.print_exc()

    def print_model_recommendations(self):
        """打印模型选择建议"""
        print("\n" + "="*60)
        print("模型选择建议")
        print("="*60)

        recommendations = {
            'lstm': "LSTM神经网络 - 适用于数据充足的波动型设备，能够捕捉复杂的时间序列模式",
            'sarima': "SARIMA时间序列模型 - 适用于数据充足的正常变化设备，具有季节性和趋势特征",
            'exponential_smoothing': "指数平滑 - 适用于扩容设备或数据不足的波动设备，对突变有较好适应性",
            'moving_average': "移动平均 - 适用于稳定设备，变化很小且可预测",
            'linear_regression': "线性回归 - 适用于数据不足的正常设备，简单有效",
            'simple_average': "简单平均 - 适用于数据严重不足的设备，使用历史平均值"
        }

        if self.analysis_results and 'recommended_models' in self.analysis_results:
            for model, count in self.analysis_results['recommended_models'].items():
                percentage = count / self.analysis_results['total_devices'] * 100
                print(f"\n{model} ({count}设备, {percentage:.1f}%):")
                print(f"  {recommendations.get(model, '未知模型')}")

        print(f"\n建议的混合建模策略:")
        print(f"1. 对于数据充足的设备(≥{self.thresholds['min_data_points']}周)，使用机器学习模型")
        print(f"2. 对于数据不足的设备(<{self.thresholds['min_data_points']}周)，使用简单统计方法")
        print(f"3. 对于扩容设备，优先使用指数平滑处理突变")
        print(f"4. 对于长期稳定设备，使用移动平均即可满足需求")


def main():
    """主函数 - 使用示例"""
    print("实占率分析工具")
    print("="*50)

    # 创建分析器实例
    analyzer = OccupancyRateAnalyzer(data_path="D:/OBD/data_processed")

    # 可以调整分类阈值
    analyzer.thresholds.update({
        'stable_cv': 0.05,      # 稳定设备变异系数阈值
        'volatile_cv': 0.15,    # 波动设备变异系数阈值
        'min_data_points': 8    # 最少数据点数
    })

    # 运行完整分析
    analyzer.run_complete_analysis(
        save_visualizations=True,  # 是否保存可视化图表
        export_results=True        # 是否导出Excel结果
    )

    # 也可以单独运行各个步骤
    # analyzer.load_data()
    # analyzer.classify_devices()
    # analyzer.generate_analysis_report()
    # analyzer.create_visualizations()
    # analyzer.export_results()


if __name__ == "__main__":
    main()
