import os
import pandas as pd
import numpy as np
import warnings
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
except:
    print("警告：无法设置中文字体，可能导致中文显示不正确")
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.callbacks import EarlyStopping
from tensorflow.keras.optimizers import Adam

# 1. 数据加载与预处理
def load_and_preprocess_data(folder_path):
    # 读取所有以'三网小区预警_'开头的Excel文件
    excel_files = [f for f in os.listdir(folder_path)
                   if f.startswith('三网小区预警_') and f.endswith('.xlsx')]
    all_data = pd.DataFrame()

    for file in excel_files:
        file_path = os.path.join(folder_path, file)
        df = pd.read_excel(file_path)
        # 筛选关键列并转换时间格式
        # 移除实占率列，后续将使用分光器利用率作为实占率
        df = df[[
            '所属设备', '小区入库时间', '覆盖的工程级的线路到达房间数',
            '分光器容量', '分光器空闲数', 'ftth终端数', 'now'
        ]]
        df['now'] = pd.to_datetime(df['now'], errors='coerce')  # 添加errors='coerce'处理异常值
        df['小区入库时间'] = pd.to_datetime(df['小区入库时间'], errors='coerce')
        all_data = pd.concat([all_data, df], ignore_index=True)
    return all_data

# 2. 特征工程
def feature_engineering(all_data):
    # 计算基本特征
    all_data['空闲率'] = all_data['分光器空闲数'] / all_data['分光器容量']
    all_data['潜在需求比'] = all_data['ftth终端数'] / (all_data['覆盖的工程级的线路到达房间数'])

    # 计算实占率（即分光器利用率）
    all_data['实占率'] = 1 - all_data['空闲率']  # 分光器利用率即实占率

    # 计算容量变化特征 - 检测扩容情况
    all_data['容量变化'] = all_data.groupby('所属设备')['分光器容量'].diff().fillna(0)

    # 计算实占率变化特征 - 检测实占率稳定性
    all_data['实占率变化'] = all_data.groupby('所属设备')['实占率'].diff().fillna(0)

    # 计算时间差特征（入库时间与预测时间的间隔）
    time_diff = (all_data['now'] - all_data['小区入库时间'])
    if time_diff.isnull().any():
        print("警告：存在无效的时间差计算，已自动填充为0")
        time_diff = time_diff.fillna(pd.Timedelta(days=0))

    all_data['入库时间差_月'] = time_diff.dt.days / 30

    # 端口预警相关特征
    # 1. 空闲端口数
    all_data['空闲端口数'] = all_data['分光器空闲数']

    # 2. 小区入网时间筛选（最近两年的新小区）
    # 直接使用入库时间差_月来判断是否为新小区（小于等于24个月）
    all_data['是新小区'] = all_data['入库时间差_月'] <= 24

    # 3. 潜在需求比筛选（低于35%）
    all_data['潜在需求比低'] = all_data['潜在需求比'] < 0.35

    # 4. 空闲端口数筛选（小于等于1）
    all_data['空闲端口数少'] = all_data['空闲端口数'] <= 1

    # 5. 综合预警条件：新小区 + 潜在需求比低 + 空闲端口数少
    all_data['预警标志'] = (all_data['是新小区'] & all_data['潜在需求比低'] & all_data['空闲端口数少'])

    # 添加历史波动性特征
    # 按设备分组计算实占率的标准差和变异系数
    volatility_stats = all_data.groupby('所属设备')['实占率'].agg(['std', 'mean'])
    # 处理标准差为0或平均值为0的情况
    volatility_stats['std'] = volatility_stats['std'].fillna(0).replace(0, 0.001)  # 避免除零错误
    volatility_stats['mean'] = volatility_stats['mean'].fillna(0).replace(0, 0.001)  # 避免除零错误
    volatility_stats['cv'] = (volatility_stats['std'] / volatility_stats['mean']).clip(0, 10)  # 限制在合理范围内

    # 将波动性统计量合并回原始数据集
    all_data = all_data.merge(
        volatility_stats.reset_index(),
        on='所属设备',
        how='left'
    )

    # 添加实占率的滑动统计量
    # 按设备分组计算滑动平均和滑动标准差
    all_data['实占率_滑动平均'] = all_data.groupby('所属设备')['实占率'].transform(
        lambda x: x.rolling(window=3, min_periods=1).mean()
    )
    all_data['实占率_滑动标准差'] = all_data.groupby('所属设备')['实占率'].transform(
        lambda x: x.rolling(window=3, min_periods=1).std()
    )
    # 处理缺失值
    all_data['实占率_滑动平均'] = all_data['实占率_滑动平均'].fillna(all_data['实占率'])
    all_data['实占率_滑动标准差'] = all_data['实占率_滑动标准差'].fillna(0)

    # 添加增长率特征
    all_data['实占率_增长率'] = all_data.groupby('所属设备')['实占率'].pct_change().fillna(0)
    all_data['实占率_增长率'] = all_data['实占率_增长率'].clip(-1, 1)  # 限制在合理范围内

    # 添加实占率的历史最大值和最小值
    all_data['实占率_历史最大值'] = all_data.groupby('所属设备')['实占率'].transform('max')
    all_data['实占率_历史最小值'] = all_data.groupby('所属设备')['实占率'].transform('min')
    all_data['实占率_范围'] = all_data['实占率_历史最大值'] - all_data['实占率_历史最小值']

    # 检查并处理无穷大和非数值
    for col in all_data.select_dtypes(include=[np.number]).columns:
        all_data[col] = all_data[col].replace([np.inf, -np.inf], np.nan)
        all_data[col] = all_data[col].fillna(all_data[col].median())

    return all_data

# 3. 按设备ID和周聚合数据
def aggregate_weekly_data(all_data):
    device_data = all_data[[
        '所属设备', 'now', '实占率', '潜在需求比',
        '入库时间差_月', '分光器容量',
        '容量变化', '实占率变化', 'std', 'mean', 'cv',
        '实占率_滑动平均', '实占率_滑动标准差',
        '实占率_增长率', '实占率_历史最大值', '实占率_历史最小值', '实占率_范围',
        # 新增特征
        '空闲端口数', '是新小区', '潜在需求比低', '空闲端口数少', '预警标志'
    ]].copy()

    # 按周分桶
    device_data['周'] = device_data['now'].dt.to_period('W').dt.start_time

    # 按设备和周聚合（静态字段取首次出现值）
    weekly_data = device_data.groupby(['所属设备', '周']).agg({
        '实占率': 'mean',
        '潜在需求比': 'mean',
        '入库时间差_月': 'mean',
        '分光器容量': 'first',
        '容量变化': 'sum',  # 汇总一周内的容量变化
        '实占率变化': 'mean',  # 平均实占率变化
        'std': 'first',  # 标准差是设备属性，取首次出现值
        'mean': 'first',  # 平均值是设备属性，取首次出现值
        'cv': 'first',  # 变异系数是设备属性，取首次出现值
        '实占率_滑动平均': 'mean',
        '实占率_滑动标准差': 'mean',
        '实占率_增长率': 'mean',
        '实占率_历史最大值': 'first',  # 历史最大值是设备属性，取首次出现值
        '实占率_历史最小值': 'first',  # 历史最小值是设备属性，取首次出现值
        '实占率_范围': 'first',  # 范围是设备属性，取首次出现值
        # 新增特征的聚合方式
        '空闲端口数': 'mean',
        '是新小区': 'first',  # 小区属性，取首次出现值
        '潜在需求比低': 'first',  # 潜在需求比属性，取首次出现值
        '空闲端口数少': 'last',  # 取最近的空闲端口数状态
        '预警标志': 'any'  # 只要有一个为True，则结果为True
    }).reset_index()

    # 添加设备类型分类
    # 根据波动性和容量变化分类设备
    weekly_data['设备类型'] = 'normal'  # 默认为正常设备

    # 标记长期稳定的设备（变异系数很小）
    weekly_data.loc[weekly_data['cv'] < 0.05, '设备类型'] = 'stable'

    # 标记最近扩容的设备
    # 按设备分组，检查最近是否有容量变化
    recent_expansion = weekly_data.sort_values(['所属设备', '周'])
    recent_expansion = recent_expansion.groupby('所属设备').tail(3)  # 取最近3周的数据
    recent_expansion = recent_expansion.groupby('所属设备')['容量变化'].sum() > 0
    recent_expansion = recent_expansion[recent_expansion].index.tolist()

    # 将最近扩容的设备标记为'expanded'
    weekly_data.loc[weekly_data['所属设备'].isin(recent_expansion), '设备类型'] = 'expanded'

    # 标记波动大的设备（变异系数很大）
    weekly_data.loc[weekly_data['cv'] > 0.2, '设备类型'] = 'volatile'

    return weekly_data

# 4. 划分训练集与测试集
def split_train_test(weekly_data):
    # 按时间顺序划分，避免数据泄漏
    train_data, test_data = train_test_split(
        weekly_data, test_size=0.2, shuffle=False
    )
    return train_data, test_data

# 5. 数据标准化
def standardize_data(train_data, test_data):
    # 使用MinMaxScaler将数据缩放到0-1之间，更适合LSTM
    scaler_features = MinMaxScaler()
    scaler_target = MinMaxScaler()

    # 基本特征
    basic_features = ['潜在需求比', '入库时间差_月', '实占率', '分光器容量', '容量变化', '实占率变化']

    # 波动性特征
    volatility_features = ['std', 'mean', 'cv', '实占率_滑动平均', '实占率_滑动标准差',
                          '实占率_增长率', '实占率_范围']

    # 合并所有需要标准化的特征
    all_features = basic_features + volatility_features

    # 只保留存在的列
    features = [f for f in all_features if f in train_data.columns and f in test_data.columns]

    # 确保所有列都是数值类型
    for feature in features:
        if not pd.api.types.is_numeric_dtype(train_data[feature]):
            print(f"警告：特征 {feature} 不是数值类型，将转换为数值类型")
            train_data[feature] = pd.to_numeric(train_data[feature], errors='coerce').fillna(0)
        if not pd.api.types.is_numeric_dtype(test_data[feature]):
            print(f"警告：特征 {feature} 不是数值类型，将转换为数值类型")
            test_data[feature] = pd.to_numeric(test_data[feature], errors='coerce').fillna(0)

    # 对特征进行缩放
    train_data[features] = scaler_features.fit_transform(train_data[features])
    test_data[features] = scaler_features.transform(test_data[features])

    # 对目标变量进行缩放（实占率）
    # 注意：我们不再返回单独的train_target和test_target，而是直接使用数据中的实占率列
    scaler_target.fit(train_data[['实占率']])

    # 创建设备类型的独热编码
    # 将设备类型转换为数值特征
    device_type_dummies = pd.get_dummies(train_data['设备类型'], prefix='设备类型')
    train_data = pd.concat([train_data, device_type_dummies], axis=1)

    # 确保测试集也有相同的独热编码列
    device_type_dummies_test = pd.get_dummies(test_data['设备类型'], prefix='设备类型')
    # 确保测试集有训练集中的所有列
    for col in device_type_dummies.columns:
        if col not in device_type_dummies_test.columns:
            device_type_dummies_test[col] = 0
    # 确保列的顺序一致
    device_type_dummies_test = device_type_dummies_test[device_type_dummies.columns]
    test_data = pd.concat([test_data, device_type_dummies_test], axis=1)

    return train_data, test_data, scaler_target

# 6. 创建序列数据
def create_sequences(data, seq_length=4, device_type=None):
    """
    将时间序列数据转换为LSTM可用的序列格式
    data: 包含特征和目标值（实占率）的DataFrame
    seq_length: 序列长度，表示使用多少个历史周的数据来预测下一周
    device_type: 可选参数，指定要处理的设备类型（'stable', 'expanded', 'volatile', 'normal'）
    """
    X, y = [], []
    device_ids = []  # 记录每个序列对应的设备ID

    # 基本特征
    basic_features = ['潜在需求比', '入库时间差_月', '实占率', '分光器容量', '容量变化', '实占率变化']

    # 波动性特征
    volatility_features = ['std', 'mean', 'cv', '实占率_滑动平均', '实占率_滑动标准差',
                          '实占率_增长率', '实占率_范围']

    # 设备类型特征（独热编码列）
    device_type_columns = [col for col in data.columns if col.startswith('设备类型_')]

    # 合并所有特征
    features = basic_features + volatility_features + device_type_columns

    # 确保所有特征列都存在于数据中
    for feature in features:
        if feature not in data.columns:
            print(f"警告：特征 {feature} 不存在于数据中，已跳过")
            features.remove(feature)

    # 确保所有特征都是数值类型
    numeric_features = []
    for feature in features:
        if feature in data.columns:
            if not pd.api.types.is_numeric_dtype(data[feature]):
                print(f"警告：特征 {feature} 不是数值类型，将转换为数值类型")
                data[feature] = pd.to_numeric(data[feature], errors='coerce').fillna(0)
            numeric_features.append(feature)
        else:
            print(f"警告：特征 {feature} 不存在于数据中，已跳过")

    features = numeric_features

    # 如果指定了设备类型，只处理该类型的设备
    if device_type is not None:
        filtered_data = data[data['设备类型'] == device_type]
        device_list = filtered_data['所属设备'].unique()
    else:
        device_list = data['所属设备'].unique()

    # 按设备分组处理
    for device in device_list:
        # 获取该设备的数据
        device_mask = data['所属设备'] == device
        device_data = data[device_mask].sort_values('周')

        # 直接使用目标值（实占率）
        device_target = device_data['实占率'].values

        # 减小序列长度要求，使更多设备可以被预测
        if len(device_data) < seq_length + 1:
            # 如果数据不足，但至少有2条记录，使用可用的数据
            if len(device_data) >= 3:  # 至少需要2条历史数据和1条目标数据
                actual_seq_length = len(device_data) - 1
                # 创建一个序列，使用所有可用的历史数据
                X.append(device_data[features].iloc[:actual_seq_length].values)
                y.append(device_target[actual_seq_length])
                device_ids.append(device)
            continue

        # 对于数据足够的设备，创建序列
        for i in range(len(device_data) - seq_length):
            X.append(device_data[features].iloc[i:i+seq_length].values)
            y.append(device_target[i+seq_length])
            device_ids.append(device)

    # 如果没有数据，返回空数组
    if len(X) == 0:
        return np.array([]), np.array([]), []

    # 将序列长度不同的数据进行填充，使其长度一致
    padded_X = []
    for x in X:
        if len(x) < seq_length:
            # 如果序列长度不足，在开头填充零
            padding = np.zeros((seq_length - len(x), x.shape[1]))
            padded_x = np.vstack((padding, x))
            padded_X.append(padded_x)
        else:
            padded_X.append(x)

    # 转换为浮点数类型
    padded_X = np.array(padded_X, dtype=np.float32)
    y = np.array(y, dtype=np.float32)

    return padded_X, y, device_ids

# 7. 构建LSTM模型
def build_lstm_model(seq_length, n_features, model_type='standard'):
    """
    构建LSTM模型
    seq_length: 序列长度
    n_features: 特征数量
    model_type: 模型类型，可选 'standard'(标准模型), 'complex'(复杂模型), 'stable'(稳定设备模型), 'expanded'(扩容设备模型)
    """
    if model_type == 'standard':
        # 标准模型
        model = Sequential([
            LSTM(64, activation='relu', return_sequences=True, input_shape=(seq_length, n_features)),
            Dropout(0.2),
            LSTM(32, activation='relu'),
            Dropout(0.2),
            Dense(16, activation='relu'),
            Dense(1, activation='sigmoid')
        ])
    elif model_type == 'complex':
        # 复杂模型 - 使用更深的网络和更多的神经元
        model = Sequential([
            LSTM(128, activation='relu', return_sequences=True, input_shape=(seq_length, n_features)),
            Dropout(0.3),
            LSTM(64, activation='relu', return_sequences=True),
            Dropout(0.3),
            LSTM(32, activation='relu'),
            Dropout(0.3),
            Dense(32, activation='relu'),
            Dropout(0.2),
            Dense(16, activation='relu'),
            Dense(1, activation='sigmoid')
        ])
    elif model_type == 'stable':
        # 稳定设备模型 - 更简单的模型，适合波动小的设备
        model = Sequential([
            LSTM(32, activation='relu', input_shape=(seq_length, n_features)),
            Dropout(0.1),
            Dense(8, activation='relu'),
            Dense(1, activation='sigmoid')
        ])
    elif model_type == 'expanded':
        # 扩容设备模型 - 特别关注最近的数据点
        model = Sequential([
            LSTM(64, activation='relu', return_sequences=False, input_shape=(seq_length, n_features)),
            Dropout(0.3),
            Dense(32, activation='relu'),
            Dense(16, activation='relu'),
            Dense(1, activation='sigmoid')
        ])
    elif model_type == 'volatile':
        # 波动大的设备模型 - 使用更复杂的网络捕捉波动性
        model = Sequential([
            LSTM(128, activation='relu', return_sequences=True, input_shape=(seq_length, n_features)),
            Dropout(0.4),
            LSTM(64, activation='relu'),
            Dropout(0.3),
            Dense(32, activation='relu'),
            Dropout(0.2),
            Dense(16, activation='relu'),
            Dense(1, activation='sigmoid')
        ])
    else:
        raise ValueError(f"Unknown model type: {model_type}")

    # 编译模型
    model.compile(
        optimizer=Adam(learning_rate=0.001),
        loss='mean_squared_error',
        metrics=['mae']
    )

    return model

# 8. 训练模型与预测
def train_and_predict(train_data, test_data, scaler_target):
    # 参数设置
    seq_length = 4  # 使用4周的历史数据预测下一周

    # 计算特征数量
    basic_features = ['潜在需求比', '入库时间差_月', '实占率', '分光器容量', '容量变化', '实占率变化']
    volatility_features = ['std', 'mean', 'cv', '实占率_滑动平均', '实占率_滑动标准差', '实占率_增长率', '实占率_范围']
    device_type_columns = [col for col in train_data.columns if col.startswith('设备类型_')]
    n_features = len(basic_features) + len(volatility_features) + len(device_type_columns)

    print(f"特征数量: {n_features}")

    # 创建不同类型设备的模型和预测结果
    device_types = ['stable', 'expanded', 'volatile', 'normal']
    models = {}
    predictions = {}
    validation_results = []
    all_histories = {}

    # 对每种设备类型分别训练模型
    for device_type in device_types:
        print(f"\n开始训练 {device_type} 类型设备的模型...")

        # 创建该类型设备的序列数据
        X_train, y_train, train_device_ids = create_sequences(train_data, seq_length, device_type)
        X_test, y_test, test_device_ids = create_sequences(test_data, seq_length, device_type)

        if len(X_train) == 0 or len(X_test) == 0:
            print(f"警告：{device_type} 类型设备数据不足，跳过该类型的模型训练")
            continue

        print(f"{device_type} 类型训练数据序列数量: {len(X_train)}, 测试数据序列数量: {len(X_test)}")
        print(f"{device_type} 类型训练数据中的设备数量: {len(set(train_device_ids))}, 测试数据中的设备数量: {len(set(test_device_ids))}")

        # 根据设备类型选择不同的模型架构
        if device_type == 'normal':
            model_architecture = 'complex'  # 正常设备使用复杂模型
        else:
            model_architecture = device_type  # 其他类型使用对应的专用模型

        # 构建模型
        model = build_lstm_model(seq_length, n_features, model_architecture)

        # 早停策略，防止过拟合
        early_stopping = EarlyStopping(
            monitor='val_loss',
            patience=10,
            restore_best_weights=True
        )

        # 训练模型
        history = model.fit(
            X_train, y_train,
            epochs=100,
            batch_size=32,
            validation_split=0.2,
            callbacks=[early_stopping],
            verbose=1
        )

        # 评估模型
        test_loss, test_mae = model.evaluate(X_test, y_test, verbose=0)
        print(f"{device_type} 类型测试集损失: {test_loss:.4f}, MAE: {test_mae:.4f}")

        # 保存模型和训练历史
        models[device_type] = model
        all_histories[device_type] = history

        # 预测测试集中的设备
        test_pred = model.predict(X_test)
        test_pred_inv = scaler_target.inverse_transform(test_pred.reshape(-1, 1)).flatten()
        test_y_inv = scaler_target.inverse_transform(y_test.reshape(-1, 1)).flatten()

        # 将预测结果与设备ID关联
        device_results = {}
        for i, device_id in enumerate(test_device_ids):
            if device_id not in device_results:
                device_results[device_id] = []
            device_results[device_id].append((test_pred_inv[i], test_y_inv[i]))

        # 计算每个设备的MSE和最终预测值
        for device_id, results in device_results.items():
            pred_values = [r[0] for r in results]
            actual_values = [r[1] for r in results]

            # 计算MSE
            mse = mean_squared_error(actual_values, pred_values)

            # 使用最后一个预测值作为未来预测
            predictions[device_id] = [pred_values[-1]]

            # 记录验证结果
            validation_results.append({
                '设备': device_id,
                '设备类型': device_type,
                'MSE': mse,
                '实际': actual_values[-1],
                '预测': pred_values[-1]
            })

    # 预测所有设备的下一周数据
    print(f"\n开始预测所有设备的下一周数据...")

    # 对所有设备进行预测，包括测试集中没有的设备
    all_devices = set(train_data['所属设备'].unique()).union(set(test_data['所属设备'].unique()))

    for device in all_devices:
        # 如果设备已经预测过，则跳过
        if device in predictions:
            continue

        # 获取该设备的所有数据，包括训练集和测试集
        device_data = pd.concat([
            train_data[train_data['所属设备'] == device],
            test_data[test_data['所属设备'] == device]
        ]).sort_values('周')

        if len(device_data) < 3:  # 至少需要2条历史数据
            continue

        # 确定设备类型
        device_type = device_data['设备类型'].iloc[0]

        # 如果该类型没有模型，使用正常设备模型
        if device_type not in models:
            device_type = 'normal'
            # 如果正常设备模型也没有，跳过该设备
            if device_type not in models:
                continue

        # 取最近的数据作为预测输入
        recent_data = device_data.iloc[-min(len(device_data), seq_length):]

        # 准备特征
        basic_features = ['潜在需求比', '入库时间差_月', '实占率', '分光器容量', '容量变化', '实占率变化']
        volatility_features = ['std', 'mean', 'cv', '实占率_滑动平均', '实占率_滑动标准差', '实占率_增长率', '实占率_范围']
        device_type_columns = [col for col in device_data.columns if col.startswith('设备类型_')]
        features = basic_features + volatility_features + device_type_columns

        # 确保所有特征都是数值类型
        numeric_features = []
        for feature in features:
            if feature in recent_data.columns:
                if not pd.api.types.is_numeric_dtype(recent_data[feature]):
                    print(f"警告：特征 {feature} 不是数值类型，将转换为数值类型")
                    recent_data[feature] = pd.to_numeric(recent_data[feature], errors='coerce').fillna(0)
                numeric_features.append(feature)

        # 如果没有有效特征，跳过该设备
        if len(numeric_features) == 0:
            print(f"警告：设备 {device} 没有有效特征，跳过预测")
            continue

        # 如果数据不足seq_length，进行填充
        if len(recent_data) < seq_length:
            input_data = recent_data[numeric_features].values
            padding = np.zeros((seq_length - len(recent_data), len(numeric_features)))
            input_data = np.vstack((padding, input_data))
        else:
            input_data = recent_data[numeric_features].values

        # 确保数据是浮点型
        input_data = input_data.astype(np.float32)

        # 预测
        input_data = input_data.reshape(1, seq_length, len(numeric_features))
        pred = models[device_type].predict(input_data, verbose=0)
        pred_inv = scaler_target.inverse_transform(pred.reshape(-1, 1)).flatten()

        # 记录预测结果
        predictions[device] = [pred_inv[0]]

    print(f"共预测了 {len(predictions)} 个设备的下一周数据")
    return predictions, validation_results, all_histories

# 9. 处理特殊情况
def handle_special_cases(weekly_data, predictions):
    """
    处理两种特殊情况：
    1. 实占率长期不变的设备
    2. 最近发生扩容的设备
    """
    adjusted_count = 0
    stable_count = 0
    expansion_count = 0

    for device in weekly_data['所属设备'].unique():
        device_data = weekly_data[weekly_data['所属设备'] == device].sort_values('周')

        if len(device_data) < 3 or device not in predictions:
            continue

        # 检查是否为长期不变的情况
        recent_data = device_data.tail(min(10, len(device_data)))  # 最近10周数据，或者全部数据如果少于10周
        unique_values = recent_data['实占率'].unique()

        # 如果最近数据只有1-2个不同的值，使用最近值作为预测
        if len(unique_values) <= 2 and device in predictions:
            # 如果预测值与最近值相差超过20%，则使用最近值
            latest_value = recent_data['实占率'].iloc[-1]
            predicted_value = predictions[device][0]

            if abs(predicted_value - latest_value) > 0.2:
                predictions[device] = [latest_value]
                print(f"设备 {device} 实占率长期稳定，使用最近值 {latest_value:.2f} 代替预测值 {predicted_value:.2f}")
                stable_count += 1
                adjusted_count += 1

        # 检查是否为最近扩容的情况
        if len(device_data) >= 4:  # 至少需要4周数据才能检测扩容
            recent_capacity_change = device_data['容量变化'].tail(3).sum()

            # 如果最近3周有扩容，调整预测值
            if recent_capacity_change > 0 and device in predictions:
                expansion_count += 1

                # 计算扩容前后的实占率
                before_expansion = device_data['实占率'].iloc[-4:-1].mean()  # 扩容前
                after_expansion = device_data['实占率'].iloc[-1]  # 扩容后
                predicted_value = predictions[device][0]

                # 如果预测值接近扩容前的实占率，而不是扩容后的实占率
                if before_expansion > after_expansion and abs(predicted_value - before_expansion) < abs(predicted_value - after_expansion):
                    # 基于扩容后的实占率进行预测，允许小幅增长
                    adjusted_pred = min(0.95, after_expansion * 1.1)

                    # 如果调整后的预测值与原预测值相差超过20%，才进行调整
                    if abs(adjusted_pred - predicted_value) > 0.2:
                        predictions[device] = [adjusted_pred]
                        print(f"设备 {device} 最近扩容，将预测值从 {predicted_value:.2f} 调整为 {adjusted_pred:.2f}")
                        adjusted_count += 1

                # 预测值超过100%的情况
                elif predicted_value > 1.0:
                    adjusted_pred = min(0.99, after_expansion * 1.1)
                    predictions[device] = [adjusted_pred]
                    print(f"设备 {device} 预测值超过100%，调整为 {adjusted_pred:.2f}")
                    adjusted_count += 1

    print(f"共检测到 {stable_count} 个实占率长期稳定的设备，{expansion_count} 个最近扩容的设备")
    print(f"共调整了 {adjusted_count} 个设备的预测值")

    # 处理预测值超过范围的情况
    for device, pred in predictions.items():
        # 预测值不应该超过100%或小于0%
        if pred[0] > 1.0:
            predictions[device] = [1.0]
        elif pred[0] < 0.0:
            predictions[device] = [0.0]

    return predictions

# 10. 输出结果与保存
def save_results(validation_results, predictions):
    # 创建结果文件夹
    import os
    results_dir = 'LSTM_results'
    if not os.path.exists(results_dir):
        os.makedirs(results_dir)

    # 将实占率转换为百分比格式
    validation_df = pd.DataFrame(validation_results)
    validation_df['实际百分比'] = (validation_df['实际'] * 100).round(2).astype(str) + '%'
    validation_df['预测百分比'] = (validation_df['预测'] * 100).round(2).astype(str) + '%'

    # 按设备类型分组统计
    type_stats = validation_df.groupby('设备类型').agg({
        'MSE': 'mean',
        '设备': 'count'
    }).rename(columns={'设备': '设备数量'})

    # 保存验证结果
    validation_file = os.path.join(results_dir, "LSTM模型验证结果.xlsx")
    validation_df.to_excel(validation_file, index=False)
    print("验证结果：\n", validation_df.head())
    print("各类型设备统计：\n", type_stats)

    # 准备预测结果输出
    output = []

    # 获取最近一周的数据，用于端口预警
    latest_data = weekly_data.sort_values('周').groupby('所属设备').last().reset_index()

    for device, forecast in predictions.items():
        # 获取设备类型
        device_type = ''
        for result in validation_results:
            if result['设备'] == device:
                device_type = result.get('设备类型', '')
                break

        # 初始化预测结果行
        prediction_row = {
            '设备': device,
            '设备类型': device_type,
            '预测实占率': forecast[0],
            '预测实占率百分比': f"{forecast[0]*100:.2f}%",
        }

        # 检查端口预警条件
        device_latest = latest_data[latest_data['所属设备'] == device]
        is_warning = False

        if len(device_latest) > 0:
            # 添加实占率和潜在需求比信息
            if '实占率' in device_latest.columns:
                prediction_row['当前实占率'] = device_latest['实占率'].iloc[0]
                prediction_row['当前实占率百分比'] = f"{device_latest['实占率'].iloc[0]*100:.2f}%"

            if '潜在需求比' in device_latest.columns:
                prediction_row['潜在需求比'] = device_latest['潜在需求比'].iloc[0]
                prediction_row['潜在需求比百分比'] = f"{device_latest['潜在需求比'].iloc[0]*100:.2f}%"

            if '空闲端口数' in device_latest.columns:
                prediction_row['空闲端口数'] = device_latest['空闲端口数'].iloc[0]

            if '是新小区' in device_latest.columns:
                prediction_row['是新小区'] = device_latest['是新小区'].iloc[0]

            # 检查是否有预警标志
            if '预警标志' in device_latest.columns:
                is_warning = device_latest['预警标志'].iloc[0]
                prediction_row['预警标志'] = is_warning

        # 根据预警标志设置提醒
        if is_warning:
            prediction_row['提醒'] = '预警需扩容'
            prediction_row['预警原因'] = '新小区+潜在需求比低+空闲端口数少'
        else:
            prediction_row['提醒'] = '正常'
            prediction_row['预警原因'] = ''

        output.append(prediction_row)

    # 保存预测结果
    output_df = pd.DataFrame(output)
    # 使用时间戳生成唯一文件名，避免文件被占用
    timestamp = pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')
    output_file = os.path.join(results_dir, f'LSTM设备实占率预测结果_{timestamp}.xlsx')
    output_df.to_excel(output_file, index=False)
    print(f'预测结果已保存至 {output_file}')

    # 统计端口预警设备数
    warning_devices = sum(1 for row in output if row.get('预警标志', False))
    print(f'端口预警设备数: {warning_devices}')

    # 输出统计信息
    print(f"总预测设备数: {len(predictions)}")

    # 统计需要扩容的设备数（基于预警标志）
    expansion_needed = sum(1 for row in output if row.get('提醒') == '预警需扩容')
    print(f"需要扩容的设备数: {expansion_needed}")

    # 按设备类型统计预测结果
    output_df['需扩容'] = output_df['提醒'] == '预警需扩容'
    type_expansion = output_df.groupby('设备类型').agg({
        '需扩容': 'sum',
        '设备': 'count'
    }).rename(columns={'设备': '设备数量'})

    print("各类型设备需扩容统计：")
    for device_type, row in type_expansion.iterrows():
        print(f"{device_type}: 共 {row['设备数量']} 个设备，需扩容 {row['需扩容']} 个设备，占比 {row['需扩容']/row['设备数量']*100:.2f}%")

# 11. 可视化训练过程和预测结果
def visualize_results(histories, validation_results):
    import os
    # 创建可视化文件夹
    vis_dir = 'LSTM_results/visualizations'
    if not os.path.exists(vis_dir):
        os.makedirs(vis_dir)

    # 将验证结果转换为DataFrame
    validation_df = pd.DataFrame(validation_results)

    # 按设备类型分组可视化
    for device_type, history in histories.items():
        # 绘制该类型设备的训练过程
        plt.figure(figsize=(12, 5))
        plt.subplot(1, 2, 1)
        plt.plot(history.history['loss'], label='训练损失')
        plt.plot(history.history['val_loss'], label='验证损失')
        plt.title(f'{device_type} 类型设备模型训练过程')
        plt.xlabel('Epochs')
        plt.ylabel('损失')
        plt.legend()

        # 绘制该类型设备的预测vs实际值
        plt.subplot(1, 2, 2)
        type_df = validation_df[validation_df['设备类型'] == device_type]
        if len(type_df) > 0:
            plt.scatter(type_df['实际'], type_df['预测'])
            plt.plot([0, 1], [0, 1], 'r--')
            plt.title(f'{device_type} 类型设备预测值 vs 实际值')
            plt.xlabel('实际值')
            plt.ylabel('预测值')

            # 添加百分比刻度
            plt.gca().xaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: '{:.0%}'.format(x)))
            plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: '{:.0%}'.format(x)))

        plt.tight_layout()
        plt.savefig(os.path.join(vis_dir, f'LSTM_{device_type}_类型设备预测结果.png'))
        plt.close()

    # 绘制所有设备的预测vs实际值
    plt.figure(figsize=(10, 8))

    # 按设备类型使用不同颜色
    colors = {'stable': 'blue', 'expanded': 'red', 'volatile': 'green', 'normal': 'purple'}

    # 绘制散点图，按设备类型着色
    for device_type, color in colors.items():
        type_df = validation_df[validation_df['设备类型'] == device_type]
        if len(type_df) > 0:
            plt.scatter(type_df['实际'], type_df['预测'],
                       color=color, alpha=0.6, label=f'{device_type} 类型设备')

    # 添加参考线
    plt.plot([0, 1], [0, 1], 'r--', alpha=0.3)

    # 添加标题和标签
    plt.title('所有设备预测值 vs 实际值')
    plt.xlabel('实际值')
    plt.ylabel('预测值')
    plt.legend()

    # 添加百分比刻度
    plt.gca().xaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: '{:.0%}'.format(x)))
    plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: '{:.0%}'.format(x)))

    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(vis_dir, 'LSTM_所有设备预测结果.png'))
    plt.close()

    # 为每个设备创建单独的可视化
    device_vis_dir = os.path.join(vis_dir, 'devices')
    if not os.path.exists(device_vis_dir):
        os.makedirs(device_vis_dir)

    # 按设备分组
    device_groups = validation_df.groupby('设备')

    # 限制可视化的设备数量，避免生成过多图表
    max_devices = 100
    device_count = 0

    for device, group in device_groups:
        if device_count >= max_devices:
            break

        # 只为有多个预测点的设备创建可视化
        if len(group) >= 3:
            device_count += 1

            plt.figure(figsize=(8, 6))

            # 获取设备类型和颜色
            device_type = group['设备类型'].iloc[0]
            color = colors.get(device_type, 'blue')

            # 绘制实际值和预测值
            plt.scatter(group['实际'], group['预测'], color=color, s=80, alpha=0.7)

            # 标记最后一个点（预测值）
            last_point = group.iloc[-1]
            plt.scatter(last_point['实际'], last_point['预测'], color='red', s=100,
                       marker='*', label='最新预测')

            # 添加参考线
            plt.plot([0, 1], [0, 1], 'r--', alpha=0.3)

            # 添加标题和标签
            plt.title(f'设备 {device} ({device_type}) 预测结果')
            plt.xlabel('实际值')
            plt.ylabel('预测值')
            plt.legend()

            # 添加百分比刻度
            plt.gca().xaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: '{:.0%}'.format(x)))
            plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: '{:.0%}'.format(x)))

            plt.grid(True, alpha=0.3)
            plt.tight_layout()

            # 使用设备ID作为文件名，替换非法字符
            safe_device_id = device.replace('/', '_').replace('\\', '_')
            plt.savefig(os.path.join(device_vis_dir, f'device_{safe_device_id}.png'))
            plt.close()

    print(f"已生成 {device_count} 个设备的单独可视化图表")

# 主流程
if __name__ == '__main__':
    # 数据文件路径
    folder_path = 'D:/OBD/data_processed'  # 输入文件夹路径

    # 执行所有步骤
    print("开始数据加载与预处理...")
    all_data = load_and_preprocess_data(folder_path)
    print(f"加载完成，共 {len(all_data)} 条记录")

    print("开始特征工程...")
    all_data = feature_engineering(all_data)
    print("特征工程完成")

    print("开始数据聚合...")
    weekly_data = aggregate_weekly_data(all_data)
    print(f"数据聚合完成，共 {len(weekly_data)} 条周记录")

    # 打印设备类型统计
    device_type_stats = weekly_data['设备类型'].value_counts()
    print("设备类型统计：")
    for device_type, count in device_type_stats.items():
        print(f"{device_type} 类型设备：{count} 个")

    print("划分训练集与测试集...")
    train_data, test_data = split_train_test(weekly_data)
    print(f"划分完成，训练集 {len(train_data)} 条，测试集 {len(test_data)} 条")

    print("数据标准化...")
    train_data, test_data, scaler_target = standardize_data(train_data, test_data)
    print("标准化完成")

    print("开始训练LSTM模型...")
    predictions, validation_results, histories = train_and_predict(train_data, test_data, scaler_target)

    if predictions is not None:
        print("处理特殊情况...")
        predictions = handle_special_cases(weekly_data, predictions)

        print("可视化结果...")
        visualize_results(histories, validation_results)

        print("保存结果...")
        save_results(validation_results, predictions)
        print("完成！")
    else:
        print("模型训练失败，请检查数据")
