"""
LSTM-R.py - 多指标预测与综合评分系统

这个文件实现了一个改进的LSTM模型，可以同时预测多个指标（实占率、空闲端口数、终端密度），
并基于这些预测值计算综合风险评分，提供多级预警机制。

主要功能：
1. 多指标预测：同时预测实占率、空闲端口数和终端密度
2. 综合评分系统：基于预测值计算风险评分
3. 多级预警机制：根据评分设置不同级别的预警
4. 可视化结果：展示多指标预测和评分结果

作者：Augment AI
日期：2025-04-24
"""

import os
import pandas as pd
import numpy as np
import warnings
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
except:
    print("警告：无法设置中文字体，可能导致中文显示不正确")

from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import LSTM, Dense, Dropout, Input, Concatenate
from tensorflow.keras.callbacks import EarlyStopping
from tensorflow.keras.optimizers import Adam

# 1. 数据加载与预处理
def load_and_preprocess_data(folder_path):
    # 读取所有以'三网小区预警_'开头的Excel文件
    excel_files = [f for f in os.listdir(folder_path)
                   if f.startswith('三网小区预警_') and f.endswith('.xlsx')]
    all_data = pd.DataFrame()

    for file in excel_files:
        file_path = os.path.join(folder_path, file)
        df = pd.read_excel(file_path)
        # 筛选关键列并转换时间格式
        # 移除实占率列，后续将使用分光器利用率作为实占率
        # 保留更多字段用于结果分析
        keep_columns = [
            '所属设备', '小区入库时间', '覆盖的工程级的线路到达房间数',
            '分光器容量', '分光器空闲数', 'ftth终端数', 'now',
            '建筑群', 'address_desc', 'area', '分光器数'
        ]

        # 只保留存在的列
        existing_columns = [col for col in keep_columns if col in df.columns]
        df = df[existing_columns]
        df['now'] = pd.to_datetime(df['now'], errors='coerce')  # 添加errors='coerce'处理异常值
        df['小区入库时间'] = pd.to_datetime(df['小区入库时间'], errors='coerce')
        all_data = pd.concat([all_data, df], ignore_index=True)
    return all_data

# 2. 特征工程
def feature_engineering(all_data):
    # 计算基本特征
    all_data['空闲率'] = all_data['分光器空闲数'] / all_data['分光器容量']
    all_data['潜在需求比'] = all_data['ftth终端数'] / (all_data['覆盖的工程级的线路到达房间数'])

    # 计算实占率（即分光器利用率）
    all_data['实占率'] = 1 - all_data['空闲率']  # 分光器利用率即实占率

    # 计算空闲端口数
    all_data['空闲端口数'] = all_data['分光器空闲数']

    # 计算入库时间差（月）
    all_data['入库时间差_月'] = ((all_data['now'] - all_data['小区入库时间']).dt.days / 30).round()

    # 端口预警相关特征
    # 1. 是否为新小区（入网时间在最近两年内）
    all_data['是新小区'] = all_data['入库时间差_月'] <= 24

    # 2. 潜在需求比是否低于35%
    all_data['潜在需求比低'] = all_data['潜在需求比'] < 0.35

    # 3. 空闲端口数是否少于等于1
    all_data['空闲端口数少'] = all_data['空闲端口数'] <= 1

    # 计算容量变化和实占率变化
    all_data = all_data.sort_values(['所属设备', 'now'])
    all_data['容量变化'] = all_data.groupby('所属设备')['分光器容量'].diff().fillna(0)
    all_data['实占率变化'] = all_data.groupby('所属设备')['实占率'].diff().fillna(0)

    return all_data

# 3. 数据聚合
def aggregate_weekly_data(all_data):
    # 按设备和时间分组
    all_data['week'] = all_data['now'].dt.isocalendar().week
    all_data['周'] = all_data['now'].dt.isocalendar().year.astype(str) + '-' + \
                      all_data['week'].astype(str).str.zfill(2)

    # 计算波动性特征
    device_groups = all_data.groupby('所属设备')

    # 计算每个设备的实占率标准差、平均值和变异系数
    stats = device_groups['实占率'].agg(['std', 'mean'])
    stats['cv'] = stats['std'] / stats['mean']  # 变异系数

    # 将统计结果合并回原始数据
    all_data = all_data.join(stats, on='所属设备')

    # 计算滚动统计量
    window = 4  # 滚动窗口大小，例如使用4周的数据

    # 初始化滚动统计量列
    all_data['实占率_滑动平均'] = np.nan
    all_data['实占率_滑动标准差'] = np.nan
    all_data['实占率_增长率'] = np.nan
    all_data['实占率_历史最大值'] = np.nan
    all_data['实占率_历史最小值'] = np.nan
    all_data['实占率_范围'] = np.nan

    # 空闲端口数的滚动统计量
    all_data['空闲端口数_滑动平均'] = np.nan
    all_data['空闲端口数_滑动标准差'] = np.nan
    all_data['空闲端口数_增长率'] = np.nan

    # 潜在需求比的滚动统计量
    all_data['潜在需求比_滑动平均'] = np.nan
    all_data['潜在需求比_滑动标准差'] = np.nan
    all_data['潜在需求比_增长率'] = np.nan

    # 按设备分组计算滚动统计量
    for device, group in device_groups:
        sorted_group = group.sort_values('now')

        # 计算实占率的滚动统计量
        sorted_group['实占率_滑动平均'] = sorted_group['实占率'].rolling(window=window, min_periods=1).mean()
        sorted_group['实占率_滑动标准差'] = sorted_group['实占率'].rolling(window=window, min_periods=1).std()
        sorted_group['实占率_增长率'] = sorted_group['实占率'].pct_change(periods=window).fillna(0)
        sorted_group['实占率_历史最大值'] = sorted_group['实占率'].expanding().max()
        sorted_group['实占率_历史最小值'] = sorted_group['实占率'].expanding().min()
        sorted_group['实占率_范围'] = sorted_group['实占率_历史最大值'] - sorted_group['实占率_历史最小值']

        # 计算空闲端口数的滚动统计量
        sorted_group['空闲端口数_滑动平均'] = sorted_group['空闲端口数'].rolling(window=window, min_periods=1).mean()
        sorted_group['空闲端口数_滑动标准差'] = sorted_group['空闲端口数'].rolling(window=window, min_periods=1).std()
        sorted_group['空闲端口数_增长率'] = sorted_group['空闲端口数'].pct_change(periods=window).fillna(0)

        # 计算潜在需求比的滚动统计量
        sorted_group['潜在需求比_滑动平均'] = sorted_group['潜在需求比'].rolling(window=window, min_periods=1).mean()
        sorted_group['潜在需求比_滑动标准差'] = sorted_group['潜在需求比'].rolling(window=window, min_periods=1).std()
        sorted_group['潜在需求比_增长率'] = sorted_group['潜在需求比'].pct_change(periods=window).fillna(0)

        # 更新原始数据中的值
        all_data.loc[sorted_group.index, '实占率_滑动平均'] = sorted_group['实占率_滑动平均']
        all_data.loc[sorted_group.index, '实占率_滑动标准差'] = sorted_group['实占率_滑动标准差']
        all_data.loc[sorted_group.index, '实占率_增长率'] = sorted_group['实占率_增长率']
        all_data.loc[sorted_group.index, '实占率_历史最大值'] = sorted_group['实占率_历史最大值']
        all_data.loc[sorted_group.index, '实占率_历史最小值'] = sorted_group['实占率_历史最小值']
        all_data.loc[sorted_group.index, '实占率_范围'] = sorted_group['实占率_范围']

        all_data.loc[sorted_group.index, '空闲端口数_滑动平均'] = sorted_group['空闲端口数_滑动平均']
        all_data.loc[sorted_group.index, '空闲端口数_滑动标准差'] = sorted_group['空闲端口数_滑动标准差']
        all_data.loc[sorted_group.index, '空闲端口数_增长率'] = sorted_group['空闲端口数_增长率']

        all_data.loc[sorted_group.index, '潜在需求比_滑动平均'] = sorted_group['潜在需求比_滑动平均']
        all_data.loc[sorted_group.index, '潜在需求比_滑动标准差'] = sorted_group['潜在需求比_滑动标准差']
        all_data.loc[sorted_group.index, '潜在需求比_增长率'] = sorted_group['潜在需求比_增长率']

    # 根据波动性分类数据
    all_data['数据变化类型'] = 'normal'  # 默认为正常类型

    # 波动大的数据
    all_data.loc[all_data['cv'] > 0.2, '数据变化类型'] = 'volatile'

    # 最近扩容的数据
    all_data.loc[all_data['容量变化'] > 0, '数据变化类型'] = 'expanded'

    # 稳定的数据（波动小）
    all_data.loc[all_data['cv'] < 0.05, '数据变化类型'] = 'stable'

    # 按设备和周聚合（静态字段取首次出现值）
    # 准备聚合字典
    agg_dict = {
        '实占率': 'mean',
        '潜在需求比': 'mean',
        '入库时间差_月': 'mean',
        '分光器容量': 'first',
        '容量变化': 'sum',  # 汇总一周内的容量变化
        '实占率变化': 'mean',  # 平均实占率变化
        '空闲端口数': 'mean',  # 平均空闲端口数
        'ftth终端数': 'mean',  # 平均ftth终端数
        '覆盖的工程级的线路到达房间数': 'first',  # 覆盖房间数（固定值）
        '小区入库时间': 'first',  # 小区入库时间
        'std': 'first',
        'mean': 'first',
        'cv': 'first',
        '实占率_滑动平均': 'last',
        '实占率_滑动标准差': 'last',
        '实占率_增长率': 'last',
        '实占率_历史最大值': 'last',
        '实占率_历史最小值': 'last',
        '实占率_范围': 'last',
        '空闲端口数_滑动平均': 'last',
        '空闲端口数_滑动标准差': 'last',
        '空闲端口数_增长率': 'last',
        '潜在需求比_滑动平均': 'last',
        '潜在需求比_滑动标准差': 'last',
        '潜在需求比_增长率': 'last',
        '是新小区': 'max',  # 取最大值，只要有一条记录是新小区，则该设备就是新小区
        '潜在需求比低': 'max',  # 同上
        '空闲端口数少': 'max',  # 同上
        '数据变化类型': 'first'  # 取第一个出现的数据变化类型
    }

    # 添加额外字段
    for col in ['建筑群', 'address_desc', 'area', '分光器数']:
        if col in all_data.columns:
            agg_dict[col] = 'first'

    # 执行聚合
    weekly_data = all_data.groupby(['所属设备', '周']).agg(agg_dict).reset_index()

    # 综合预警条件：新小区 + 潜在需求比低 + 空闲端口数少
    weekly_data['预警标志'] = (weekly_data['是新小区'] & weekly_data['潜在需求比低'] & weekly_data['空闲端口数少'])

    return weekly_data

# 4. 数据标准化
def standardize_data(train_data, test_data):
    # 使用MinMaxScaler将数据缩放到0-1之间，更适合LSTM
    scaler_features = MinMaxScaler()

    # 定义需要标准化的特征
    # 基本特征
    basic_features = [
        '潜在需求比', '入库时间差_月', '实占率',
        '分光器容量', '容量变化', '实占率变化', '空闲端口数'
    ]

    # 波动性特征
    volatility_features = [
        'std', 'mean', 'cv',
        '实占率_滑动平均', '实占率_滑动标准差', '实占率_增长率', '实占率_范围',
        '空闲端口数_滑动平均', '空闲端口数_滑动标准差', '空闲端口数_增长率',
        '潜在需求比_滑动平均', '潜在需求比_滑动标准差', '潜在需求比_增长率'
    ]

    # 合并所有需要标准化的特征
    all_features = basic_features + volatility_features

    # 只保留存在的列
    features = [f for f in all_features if f in train_data.columns and f in test_data.columns]

    # 确保所有列都是数值类型
    for feature in features:
        if not pd.api.types.is_numeric_dtype(train_data[feature]):
            print(f"警告：特征 {feature} 不是数值类型，将转换为数值类型")
            train_data[feature] = pd.to_numeric(train_data[feature], errors='coerce').fillna(0)
        if not pd.api.types.is_numeric_dtype(test_data[feature]):
            print(f"警告：特征 {feature} 不是数值类型，将转换为数值类型")
            test_data[feature] = pd.to_numeric(test_data[feature], errors='coerce').fillna(0)

    # 检查并处理无穷大和非数值
    for col in features:
        train_data[col] = train_data[col].replace([np.inf, -np.inf], np.nan)
        train_data[col] = train_data[col].fillna(train_data[col].median() if not pd.isna(train_data[col].median()) else 0)

        test_data[col] = test_data[col].replace([np.inf, -np.inf], np.nan)
        test_data[col] = test_data[col].fillna(test_data[col].median() if not pd.isna(test_data[col].median()) else 0)

    # 对特征进行缩放
    train_data[features] = scaler_features.fit_transform(train_data[features])
    test_data[features] = scaler_features.transform(test_data[features])

    # 创建目标值的缩放器
    scaler_occupancy = MinMaxScaler()
    scaler_demand = MinMaxScaler()

    # 对空闲端口数使用StandardScaler而不是MinMaxScaler
    # StandardScaler更适合处理数值范围较大的特征
    scaler_ports = StandardScaler()

    # 对目标变量进行缩放
    scaler_occupancy.fit(train_data[['实占率']])

    # 对空闲端口数进行特殊处理
    # 获取最大分光器容量作为参考
    max_capacity = train_data['分光器容量'].max()
    if max_capacity <= 0 or pd.isna(max_capacity):
        max_capacity = 100  # 默认值

    # 对空闲端口数进行对数变换处理
    # 注意：由于空闲端口数可能为0，我们使用log1p（log(1+x)）而不是log
    # 这样可以避免取对0的问题
    train_data['log_ports'] = np.log1p(train_data['空闲端口数'])
    test_data['log_ports'] = np.log1p(test_data['空闲端口数'])  # 同样对测试数据进行对数变换

    # 使用StandardScaler对对数变换后的空闲端口数进行缩放
    scaler_ports.fit(train_data[['log_ports']])

    scaler_demand.fit(train_data[['潜在需求比']])

    # 创建数据变化类型的独热编码
    data_type_dummies = pd.get_dummies(train_data['数据变化类型'], prefix='数据变化类型')
    train_data = pd.concat([train_data, data_type_dummies], axis=1)

    # 确保测试集也有相同的独热编码列
    data_type_dummies_test = pd.get_dummies(test_data['数据变化类型'], prefix='数据变化类型')
    # 确保测试集有训练集中的所有列
    for col in data_type_dummies.columns:
        if col not in data_type_dummies_test.columns:
            data_type_dummies_test[col] = 0
    # 确保列的顺序一致
    data_type_dummies_test = data_type_dummies_test[data_type_dummies.columns]
    test_data = pd.concat([test_data, data_type_dummies_test], axis=1)

    # 返回标准化后的数据和缩放器
    scalers = {
        'features': scaler_features,
        'occupancy': scaler_occupancy,
        'ports': scaler_ports,
        'demand': scaler_demand
    }

    return train_data, test_data, scalers

# 5. 创建序列数据
def create_sequences(data, seq_length=4, data_type=None):
    """
    将时间序列数据转换为LSTM可用的序列格式
    data: 包含特征和目标值的DataFrame
    seq_length: 序列长度，表示使用多少个历史周的数据来预测下一周
    data_type: 可选参数，指定要处理的数据变化类型（'stable', 'expanded', 'volatile', 'normal'）
    """
    # 如果指定了数据变化类型，只处理该类型的数据
    if data_type:
        data = data[data['数据变化类型'] == data_type]

    # 获取所有唯一的设备
    device_list = data['所属设备'].unique()

    # 基本特征
    basic_features = [
        '潜在需求比', '入库时间差_月', '实占率',
        '分光器容量', '容量变化', '实占率变化', '空闲端口数'
    ]

    # 波动性特征
    volatility_features = [
        'std', 'mean', 'cv',
        '实占率_滑动平均', '实占率_滑动标准差', '实占率_增长率', '实占率_范围',
        '空闲端口数_滑动平均', '空闲端口数_滑动标准差', '空闲端口数_增长率',
        '潜在需求比_滑动平均', '潜在需求比_滑动标准差', '潜在需求比_增长率'
    ]

    # 数据变化类型特征
    data_type_columns = [col for col in data.columns if col.startswith('数据变化类型_')]

    # 合并所有特征
    all_features = basic_features + volatility_features + data_type_columns

    # 确保所有特征都是数值类型
    numeric_features = []
    for feature in all_features:
        if feature in data.columns:
            if not pd.api.types.is_numeric_dtype(data[feature]):
                print(f"警告：特征 {feature} 不是数值类型，将转换为数值类型")
                data[feature] = pd.to_numeric(data[feature], errors='coerce').fillna(0)
            numeric_features.append(feature)
        else:
            print(f"警告：特征 {feature} 不存在于数据中，已跳过")

    features = numeric_features

    # 初始化序列容器
    X = []
    y_occupancy = []  # 实占率目标
    y_ports = []      # 空闲端口数目标
    y_demand = []     # 潜在需求比目标
    device_ids = []   # 记录每个序列对应的设备ID

    # 按设备分组处理
    for device in device_list:
        # 获取该设备的数据并按时间排序
        device_mask = data['所属设备'] == device
        device_data = data[device_mask].sort_values('周')

        # 如果数据点少于序列长度+1，跳过该设备
        if len(device_data) < seq_length + 1:
            continue

        # 创建输入序列和目标值
        for i in range(len(device_data) - seq_length):
            # 输入序列（前seq_length周的数据）
            seq = device_data.iloc[i:i+seq_length][features].values

            # 目标值（下一周的实占率、空闲端口数和潜在需求比）
            next_week = device_data.iloc[i+seq_length]
            target_occupancy = next_week['实占率']
            # 使用对数变换后的空闲端口数
            target_ports = next_week['log_ports']

            # 如果有ftth终端数和覆盖房间数，计算潜在需求比
            if 'ftth终端数' in next_week and '覆盖的工程级的线路到达房间数' in next_week:
                ftth = next_week['ftth终端数']
                covered_rooms = next_week['覆盖的工程级的线路到达房间数']
                if covered_rooms > 0:
                    target_demand = ftth / covered_rooms
                else:
                    target_demand = 0
            else:
                target_demand = next_week['潜在需求比']

            X.append(seq)
            y_occupancy.append(target_occupancy)
            y_ports.append(target_ports)
            y_demand.append(target_demand)
            device_ids.append(device)

    # 转换为numpy数组，并指定数据类型
    X = np.array(X, dtype=np.float32)
    y_occupancy = np.array(y_occupancy, dtype=np.float32)
    y_ports = np.array(y_ports, dtype=np.float32)
    y_demand = np.array(y_demand, dtype=np.float32)

    # 将目标值转换为2D数组（需要为缩放器）
    y_occupancy = y_occupancy.reshape(-1, 1)
    y_ports = y_ports.reshape(-1, 1)
    y_demand = y_demand.reshape(-1, 1)

    # 将多个目标值组合成一个数组
    y_combined = np.hstack((y_occupancy, y_ports, y_demand))

    return X, y_combined, device_ids

# 6. 构建多指标预测模型
def build_multi_output_lstm_model(seq_length, n_features, data_type='normal'):
    """
    构建多输出的LSTM模型，同时预测实占率、空闲端口数和潜在需求比
    根据数据变化类型使用不同的模型架构
    """
    # 共享的输入层
    input_layer = Input(shape=(seq_length, n_features))

    # 根据数据变化类型选择不同的模型架构
    if data_type == 'stable':
        # 稳定设备模型 - 简单架构，因为数据模式相对稳定
        lstm_layer = LSTM(32, activation='relu')(input_layer)
        dropout_layer = Dropout(0.2)(lstm_layer)

        # 实占率预测分支
        occupancy_dense = Dense(16, activation='relu')(dropout_layer)
        occupancy_output = Dense(1, name='occupancy_output', activation='sigmoid')(occupancy_dense)

        # 空闲端口数预测分支
        ports_dense = Dense(16, activation='relu')(dropout_layer)
        ports_output = Dense(1, name='ports_output', activation='linear')(ports_dense)

        # 潜在需求比预测分支
        demand_dense = Dense(16, activation='relu')(dropout_layer)
        demand_output = Dense(1, name='demand_output', activation='sigmoid')(demand_dense)

    elif data_type == 'expanded':
        # 扩容设备模型 - 特别关注最近的数据点
        lstm_layer = LSTM(64, activation='relu')(input_layer)
        dropout_layer = Dropout(0.3)(lstm_layer)

        # 实占率预测分支
        occupancy_dense = Dense(32, activation='relu')(dropout_layer)
        occupancy_dense = Dense(16, activation='relu')(occupancy_dense)
        occupancy_output = Dense(1, name='occupancy_output', activation='sigmoid')(occupancy_dense)

        # 空闲端口数预测分支
        ports_dense = Dense(32, activation='relu')(dropout_layer)
        ports_dense = Dense(16, activation='relu')(ports_dense)
        ports_output = Dense(1, name='ports_output', activation='linear')(ports_dense)

        # 潜在需求比预测分支
        demand_dense = Dense(32, activation='relu')(dropout_layer)
        demand_dense = Dense(16, activation='relu')(demand_dense)
        demand_output = Dense(1, name='demand_output', activation='sigmoid')(demand_dense)

    elif data_type == 'volatile':
        # 波动大的设备模型 - 使用更复杂的网络捕捉波动性
        lstm_layer1 = LSTM(128, activation='relu', return_sequences=True)(input_layer)
        dropout_layer1 = Dropout(0.4)(lstm_layer1)
        lstm_layer2 = LSTM(64, activation='relu')(dropout_layer1)
        dropout_layer2 = Dropout(0.3)(lstm_layer2)

        # 实占率预测分支
        occupancy_dense = Dense(32, activation='relu')(dropout_layer2)
        occupancy_dense = Dropout(0.2)(occupancy_dense)
        occupancy_dense = Dense(16, activation='relu')(occupancy_dense)
        occupancy_output = Dense(1, name='occupancy_output', activation='sigmoid')(occupancy_dense)

        # 空闲端口数预测分支
        ports_dense = Dense(32, activation='relu')(dropout_layer2)
        ports_dense = Dropout(0.2)(ports_dense)
        ports_dense = Dense(16, activation='relu')(ports_dense)
        ports_output = Dense(1, name='ports_output', activation='linear')(ports_dense)

        # 潜在需求比预测分支
        demand_dense = Dense(32, activation='relu')(dropout_layer2)
        demand_dense = Dropout(0.2)(demand_dense)
        demand_dense = Dense(16, activation='relu')(demand_dense)
        demand_output = Dense(1, name='demand_output', activation='sigmoid')(demand_dense)

    else:  # normal
        # 正常设备模型 - 平衡的架构
        lstm_layer = LSTM(64, activation='relu')(input_layer)
        dropout_layer = Dropout(0.3)(lstm_layer)

        # 实占率预测分支
        occupancy_dense = Dense(32, activation='relu')(dropout_layer)
        occupancy_output = Dense(1, name='occupancy_output', activation='sigmoid')(occupancy_dense)

        # 空闲端口数预测分支
        ports_dense = Dense(32, activation='relu')(dropout_layer)
        ports_output = Dense(1, name='ports_output', activation='linear')(ports_dense)

        # 潜在需求比预测分支
        demand_dense = Dense(32, activation='relu')(dropout_layer)
        demand_output = Dense(1, name='demand_output', activation='sigmoid')(demand_dense)

    # 创建多输出模型
    model = Model(
        inputs=input_layer,
        outputs=[occupancy_output, ports_output, demand_output]
    )

    # 编译模型
    model.compile(
        optimizer=Adam(learning_rate=0.001),
        loss={
            'occupancy_output': 'mse',
            'ports_output': 'huber',  # 使用Huber损失，对异常值更鲁棒
            'demand_output': 'mse'
        },
        loss_weights={
            'occupancy_output': 1.0,
            'ports_output': 1.5,  # 增加空闲端口数预测的权重
            'demand_output': 1.0
        },
        metrics={
            'occupancy_output': ['mae'],
            'ports_output': ['mae'],
            'demand_output': ['mae']
        }
    )

    return model

# 7. 训练模型与预测
def train_and_predict(train_data, test_data, scalers, all_data):
    # 设置序列长度
    seq_length = 4  # 使用4周的历史数据预测下一周

    # 初始化结果容器
    predictions = {}  # 存储预测结果
    validation_results = []  # 存储验证结果
    histories = {}  # 存储训练历史

    # 定义早停回调
    early_stopping = EarlyStopping(
        monitor='val_loss',
        patience=10,
        restore_best_weights=True
    )

    # 对每种数据变化类型分别训练模型
    for data_type in ['stable', 'expanded', 'volatile', 'normal']:
        print(f"开始训练{data_type}类型数据的模型...")

        # 创建该类型数据的序列
        print(f"为{data_type}类型数据创建序列...")
        X_train, y_train, _ = create_sequences(train_data, seq_length, data_type)  # 使用_忽略未使用的返回值
        X_test, y_test, test_device_ids = create_sequences(test_data, seq_length, data_type)

        # 打印数据类型信息
        print(f"X_train 类型: {type(X_train)}, 形状: {X_train.shape if hasattr(X_train, 'shape') else 'N/A'}, 数据类型: {X_train.dtype if hasattr(X_train, 'dtype') else 'N/A'}")
        print(f"y_train 类型: {type(y_train)}, 形状: {y_train.shape if hasattr(y_train, 'shape') else 'N/A'}, 数据类型: {y_train.dtype if hasattr(y_train, 'dtype') else 'N/A'}")

        # 如果没有足够的数据，跳过该类型
        if len(X_train) < 10 or len(X_test) < 5:
            print(f"警告：{data_type}类型数据不足，跳过训练")
            continue

        # 计算特征数量
        n_features = X_train.shape[2]

        # 构建模型
        model = build_multi_output_lstm_model(seq_length, n_features, data_type)

        # 将目标值分解为三个单独的目标
        try:
            # 确保数据是浮点数类型
            y_train = y_train.astype(np.float32)
            y_test = y_test.astype(np.float32)

            y_train_occupancy = y_train[:, 0].reshape(-1, 1)
            y_train_ports = y_train[:, 1].reshape(-1, 1)
            y_train_demand = y_train[:, 2].reshape(-1, 1)

            y_test_occupancy = y_test[:, 0].reshape(-1, 1)
            y_test_ports = y_test[:, 1].reshape(-1, 1)
            y_test_demand = y_test[:, 2].reshape(-1, 1)

            # 检查并处理非数值
            for arr in [y_train_occupancy, y_train_ports, y_train_demand, y_test_occupancy, y_test_ports, y_test_demand]:
                arr[np.isnan(arr)] = 0
                arr[np.isinf(arr)] = 0
        except Exception as e:
            print(f"警告：处理目标值时出错：{str(e)}")
            # 如果出错，跳过该类型设备
            continue

        # 检查数据是否包含非数值或无穷大
        try:
            # 检查训练数据
            if len(X_train) > 0:
                if np.isnan(X_train.astype(float)).any() or np.isinf(X_train.astype(float)).any():
                    print(f"警告：训练数据中包含非数值或无穷大值，将替换为0")
                    X_train = np.nan_to_num(X_train.astype(float))

            # 检查测试数据
            if len(X_test) > 0:
                if np.isnan(X_test.astype(float)).any() or np.isinf(X_test.astype(float)).any():
                    print(f"警告：测试数据中包含非数值或无穷大值，将替换为0")
                    X_test = np.nan_to_num(X_test.astype(float))
        except Exception as e:
            print(f"警告：检查数据时出错：{str(e)}")

        # 训练模型
        history = model.fit(
            X_train,
            {
                'occupancy_output': y_train_occupancy,
                'ports_output': y_train_ports,
                'demand_output': y_train_demand
            },
            epochs=100,
            batch_size=32,
            validation_split=0.2,
            callbacks=[early_stopping],
            verbose=1
        )

        # 记录训练历史
        histories[data_type] = history.history

        # 评估模型
        evaluation = model.evaluate(
            X_test,
            {
                'occupancy_output': y_test_occupancy,
                'ports_output': y_test_ports,
                'demand_output': y_test_demand
            },
            verbose=0
        )

        # 输出评估结果
        print(f"{data_type}类型数据模型评估结果:")
        print(f"  总体损失: {evaluation[0]:.6f}")
        print(f"  实占率预测损失: {evaluation[1]:.6f}, MAE: {evaluation[4]:.6f}")
        print(f"  空闲端口数预测损失: {evaluation[2]:.6f}, MAE: {evaluation[5]:.6f}")
        print(f"  潜在需求比预测损失: {evaluation[3]:.6f}, MAE: {evaluation[6]:.6f}")

        # 预测测试集
        test_predictions = model.predict(X_test)

        # 反向缩放预测结果
        pred_occupancy = scalers['occupancy'].inverse_transform(test_predictions[0])
        # 先反向缩放对数变换的空闲端口数
        log_pred_ports = scalers['ports'].inverse_transform(test_predictions[1])
        # 然后将对数空间的值转换回原始空间（使用expm1，即exp(x)-1，这是log1p的逆操作）
        raw_pred_ports = np.expm1(log_pred_ports)
        pred_demand = scalers['demand'].inverse_transform(test_predictions[2])

        # 反向缩放实际值
        true_occupancy = scalers['occupancy'].inverse_transform(y_test_occupancy)
        # 同样对实际空闲端口数进行反向对数变换
        log_true_ports = scalers['ports'].inverse_transform(y_test_ports)
        true_ports = np.expm1(log_true_ports)
        true_demand = scalers['demand'].inverse_transform(y_test_demand)

        # 处理空闲端口数预测值
        pred_ports = np.copy(raw_pred_ports)

        # 获取最大端口数作为参考
        max_ports = []
        for i, device_id in enumerate(test_device_ids):
            # 从原始数据中获取分光器容量
            device_data_orig = all_data[all_data['所属设备'] == device_id]
            if len(device_data_orig) > 0 and '分光器容量' in device_data_orig.columns:
                max_port = device_data_orig['分光器容量'].iloc[-1]
                max_ports.append(max_port if max_port > 0 else 32)
            else:
                # 如果原始数据中没有，尝试从测试数据中获取
                device_data = test_data[test_data['所属设备'] == device_id]
                if len(device_data) > 0 and '分光器容量' in device_data.columns:
                    max_port = device_data['分光器容量'].iloc[-1]
                    max_ports.append(max_port if max_port > 0 else 32)
                else:
                    max_ports.append(32)  # 默认值
            print(f"DEBUG [容量获取] - 设备: {device_id}, 分光器容量: {max_ports[-1]}")

        # 获取当前空闲端口数和实占率
        current_ports = []
        current_occupancy = []
        for i, device_id in enumerate(test_device_ids):
            device_data = test_data[test_data['所属设备'] == device_id]
            if len(device_data) > 0 and '空闲端口数' in device_data.columns:
                current_ports.append(device_data['空闲端口数'].iloc[-1])
            else:
                current_ports.append(None)

            if len(device_data) > 0 and '实占率' in device_data.columns:
                current_occupancy.append(device_data['实占率'].iloc[-1])
            else:
                current_occupancy.append(0.5)  # 默认值

        for i in range(len(pred_ports)):
            max_port = max_ports[i] if i < len(max_ports) else 32

            # 结合两种方法预测空闲端口数
            max_port = max_ports[i] if i < len(max_ports) else 32
            pred_occ = pred_occupancy[i][0]

            # 方法1: 直接使用模型预测的空闲端口数
            model_pred_ports = raw_pred_ports[i][0]
            device_id = test_device_ids[i] if i < len(test_device_ids) else 'Unknown'
            print(f"DEBUG [{device_id}] - 原始预测值: {model_pred_ports:.6f}")

            # 方法2: 使用分光器容量和预测实占率计算空闲端口数
            calc_pred_ports = max_port * (1 - pred_occ)
            print(f"DEBUG [{device_id}] - 分光器容量: {max_port}, 预测实占率: {pred_occ:.6f}, 计算空闲端口数: {calc_pred_ports:.6f}")

            # 直接使用计算值作为预测结果
            pred_ports[i][0] = calc_pred_ports
            print(f"DEBUG [{device_id}] - 使用计算值: {pred_ports[i][0]:.6f}")

            # 确保预测值在合理范围内，允许最小值为0
            original_value = pred_ports[i][0]
            pred_ports[i][0] = max(0, min(pred_ports[i][0], max_port - 1))
            print(f"DEBUG [{device_id}] - 范围限制前: {original_value:.6f}, 范围限制后: {pred_ports[i][0]:.6f}")

            # 转换为整数
            original_value = pred_ports[i][0]
            pred_ports[i][0] = int(round(pred_ports[i][0]))
            print(f"DEBUG [{device_id}] - 四舍五入前: {original_value:.6f}, 四舍五入后: {pred_ports[i][0]}")



            # 打印部分调试信息
            if i < 5:  # 只打印前5个作为示例
                print(f"Test device: {test_device_ids[i] if i < len(test_device_ids) else 'Unknown'}, Raw pred ports: {raw_pred_ports[i][0]}, Max capacity: {max_port}, Final pred ports: {pred_ports[i][0]}")

        # 反向缩放实际值
        true_occupancy = scalers['occupancy'].inverse_transform(y_test_occupancy)
        true_ports = scalers['ports'].inverse_transform(y_test_ports)
        true_demand = scalers['demand'].inverse_transform(y_test_demand)

        # 计算每个设备的预测误差
        for i, device_id in enumerate(test_device_ids):
            # 记录验证结果
            validation_results.append({
                '设备': device_id,
                '数据变化类型': data_type,
                '实际实占率': true_occupancy[i][0],
                '预测实占率': pred_occupancy[i][0],
                '实占率误差': abs(true_occupancy[i][0] - pred_occupancy[i][0]),
                '实际空闲端口数': true_ports[i][0],
                '预测空闲端口数': pred_ports[i][0],
                '空闲端口数误差': abs(true_ports[i][0] - pred_ports[i][0]),
                '实际潜在需求比': true_demand[i][0],
                '预测潜在需求比': pred_demand[i][0],
                '潜在需求比误差': abs(true_demand[i][0] - pred_demand[i][0])
            })

        # 预测最新一周的下一周数据
        latest_data = train_data[train_data['数据变化类型'] == data_type].copy()

        # 按设备分组处理
        for device in latest_data['所属设备'].unique():
            device_data = latest_data[latest_data['所属设备'] == device].sort_values('周')

            # 如果数据点少于序列长度，跳过该设备
            if len(device_data) < seq_length:
                continue

            # 准备特征
            basic_features = [
                '潜在需求比', '入库时间差_月', '实占率',
                '分光器容量', '容量变化', '实占率变化', '空闲端口数'
            ]
            volatility_features = [
                'std', 'mean', 'cv',
                '实占率_滑动平均', '实占率_滑动标准差', '实占率_增长率', '实占率_范围',
                '空闲端口数_滑动平均', '空闲端口数_滑动标准差', '空闲端口数_增长率',
                '潜在需求比_滑动平均', '潜在需求比_滑动标准差', '潜在需求比_增长率'
            ]
            data_type_columns = [col for col in device_data.columns if col.startswith('数据变化类型_')]
            all_features = basic_features + volatility_features + data_type_columns

            # 只保留存在的列
            features = [f for f in all_features if f in device_data.columns]

            # 取最近的数据作为预测输入
            recent_data = device_data.iloc[-seq_length:][features]

            # 处理非数值和无穷大
            for col in features:
                if col in recent_data.columns:
                    recent_data[col] = pd.to_numeric(recent_data[col], errors='coerce')
                    recent_data[col] = recent_data[col].replace([np.inf, -np.inf], np.nan)
                    recent_data[col] = recent_data[col].fillna(recent_data[col].median() if not pd.isna(recent_data[col].median()) else 0)

            # 转换为numpy数组并确保是浮点型
            recent_data = recent_data.values.astype(np.float32)

            # 预测
            input_data = recent_data.reshape(1, seq_length, len(features))
            device_predictions = model.predict(input_data)

            # 反向缩放预测结果
            pred_occupancy = scalers['occupancy'].inverse_transform(device_predictions[0])[0][0]

            # 空闲端口数预测处理
            # 获取当前空闲端口数作为参考
            current_ports = None
            if len(device_data) > 0 and '空闲端口数' in device_data.columns:
                current_ports = device_data['空闲端口数'].iloc[-1]

            # 获取分光器容量作为阈值参考
            max_ports = 32  # 默认最大端口数

            # 从原始数据中获取分光器容量
            device_data_orig = all_data[all_data['所属设备'] == device]
            if len(device_data_orig) > 0 and '分光器容量' in device_data_orig.columns:
                max_capacity = device_data_orig['分光器容量'].iloc[-1]
                if max_capacity > 0:
                    max_ports = max_capacity
            # 如果原始数据中没有，尝试从训练数据中获取
            elif len(device_data) > 0 and '分光器容量' in device_data.columns:
                max_capacity = device_data['分光器容量'].iloc[-1]
                if max_capacity > 0:
                    max_ports = max_capacity

            print(f"DEBUG [容量获取-最新周] - 设备: {device}, 分光器容量: {max_ports}")

            # 反向缩放预测的空闲端口数
            # 先反向缩放对数变换的空闲端口数
            log_pred_ports = scalers['ports'].inverse_transform(device_predictions[1])[0][0]
            # 然后将对数空间的值转换回原始空间
            raw_pred_ports = np.expm1(log_pred_ports)

            # 直接使用分光器容量和实占率来计算空闲端口数
            # 获取当前实占率和预测实占率
            current_occupancy = device_data['实占率'].iloc[-1] if '实占率' in device_data.columns else 0.5
            pred_occupancy = scalers['occupancy'].inverse_transform(device_predictions[0])[0][0]

            # 结合两种方法预测空闲端口数

            # 方法1: 直接使用模型预测的空闲端口数
            model_pred_ports = raw_pred_ports
            print(f"DEBUG [最新周预测 {device}] - 原始预测值: {model_pred_ports:.6f}")

            # 方法2: 使用分光器容量和预测实占率计算空闲端口数
            calc_pred_ports = max_ports * (1 - pred_occupancy)
            print(f"DEBUG [最新周预测 {device}] - 分光器容量: {max_ports}, 预测实占率: {pred_occupancy:.6f}, 计算空闲端口数: {calc_pred_ports:.6f}")

            # 直接使用计算值作为预测结果
            pred_ports = int(round(calc_pred_ports))
            print(f"DEBUG [最新周预测 {device}] - 使用计算值: {calc_pred_ports:.6f}, 四舍五入后: {pred_ports}")

            # 确保预测值在合理范围内，允许最小值为0
            original_value = pred_ports
            pred_ports = max(0, min(pred_ports, int(max_ports - 1)))
            print(f"DEBUG [最新周预测 {device}] - 范围限制前: {original_value}, 范围限制后: {pred_ports}")

            # 确保空闲端口数不为负数
            if pred_ports < 0:
                pred_ports = 0



            # 打印调试信息
            print(f"Device: {device}, Raw pred ports: {raw_pred_ports}, Max capacity: {max_ports}, Final pred ports: {pred_ports}")

            pred_demand = scalers['demand'].inverse_transform(device_predictions[2])[0][0]

            # 存储预测结果
            predictions[device] = (pred_occupancy, pred_ports, pred_demand)

    return predictions, validation_results, histories

# 8. 综合评分系统
def calculate_risk_score(occupancy, ports, demand, is_new_district=False):
    """
    计算综合风险评分

    参数:
    - occupancy: 实占率（浮点数，0-1之间）
    - ports: 空闲端口数（整数）
    - demand: 潜在需求比（浮点数，0-1之间）
    - is_new_district: 是否为新小区（布尔值）

    返回:
    - score: 风险评分（0-100之间）
    - level: 风险级别（安全、注意、警告、紧急）
    - reasons: 风险原因列表
    """
    # 初始化权重
    weights = {
        'occupancy': 0.5,  # 实占率权重
        'ports': 0.3,     # 空闲端口数权重
        'demand': 0.2     # 潜在需求比权重
    }

    # 初始化风险原因列表
    reasons = []

    # 计算实占率评分（0-100）
    if occupancy >= 0.95:
        occupancy_score = 100
        reasons.append("实占率过高（≥ 95%）")
    elif occupancy >= 0.9:
        occupancy_score = 80 + (occupancy - 0.9) * 400  # 80-100线性映射
        reasons.append("实占率偏高（≥ 90%）")
    elif occupancy >= 0.8:
        occupancy_score = 60 + (occupancy - 0.8) * 200  # 60-80线性映射
        reasons.append("实占率较高（≥ 80%）")
    elif occupancy >= 0.7:
        occupancy_score = 40 + (occupancy - 0.7) * 200  # 40-60线性映射
    elif occupancy >= 0.5:
        occupancy_score = 20 + (occupancy - 0.5) * 100  # 20-40线性映射
    else:
        occupancy_score = occupancy * 40  # 0-20线性映射

    # 计算空闲端口数评分（0-100）
    if ports <= 0:
        ports_score = 100
        reasons.append("无空闲端口")
    elif ports <= 1:
        ports_score = 90
        reasons.append("空闲端口数仅剩 1 个")
    elif ports <= 2:
        ports_score = 70
        reasons.append("空闲端口数仅剩 2 个")
    elif ports <= 4:
        ports_score = 50
        reasons.append("空闲端口数仅剩 3-4 个")
    elif ports <= 8:
        ports_score = 30
    else:
        ports_score = max(0, 30 - (ports - 8) * 2)  # 端口数越多，评分越低，最低为0

    # 计算潜在需求比评分（0-100）
    if demand < 0.2:
        demand_score = 90
        reasons.append("潜在需求比非常低（< 20%）")
    elif demand < 0.35:
        demand_score = 70
        reasons.append("潜在需求比偏低（< 35%）")
    elif demand < 0.5:
        demand_score = 50
    elif demand < 0.7:
        demand_score = 30
    else:
        demand_score = 10

    # 新小区加权
    if is_new_district:
        # 新小区的空闲端口数和潜在需求比权重增加
        weights['ports'] += 0.1
        weights['demand'] += 0.1
        weights['occupancy'] -= 0.2  # 保持总权重为1
        reasons.append("新小区（入网时间≤ 24个月）")

    # 计算加权总分
    total_score = (
        weights['occupancy'] * occupancy_score +
        weights['ports'] * ports_score +
        weights['demand'] * demand_score
    )

    # 确定风险级别
    if total_score >= 80:
        level = "紧急"
    elif total_score >= 60:
        level = "警告"
    elif total_score >= 40:
        level = "注意"
    else:
        level = "安全"

    return total_score, level, reasons

# 9. 生成预测结果和风险评分
def generate_results(predictions, validation_results, weekly_data, missing_devices=None, all_data=None):
    """
    生成预测结果和风险评分
    """

    # 准备预测结果输出
    output = []

    # 获取最近一周的数据，用于风险评分
    latest_data = weekly_data.sort_values('周').groupby('所属设备').last().reset_index()

    # 创建一个设备到数据变化类型的映射
    device_to_data_type = {}
    for _, row in latest_data.iterrows():
        device = row['所属设备']
        if '数据变化类型' in row:
            device_to_data_type[device] = row['数据变化类型']

    # 添加来自validation_results的数据变化类型
    for result in validation_results:
        device = result['设备']
        if device not in device_to_data_type and '数据变化类型' in result:
            device_to_data_type[device] = result.get('数据变化类型', '')

    # 处理有预测结果的设备
    for device, prediction in predictions.items():
        # 获取数据变化类型
        data_type = device_to_data_type.get(device, 'normal')  # 默认为normal类型

        # 记录该设备已处理
        if missing_devices is not None and device in missing_devices:
            missing_devices.remove(device)

        # 解析预测结果
        pred_occupancy, pred_ports, pred_demand = prediction

        # 获取分光器容量
        max_port = 32  # 默认值
        if all_data is not None:
            device_data_orig = all_data[all_data['所属设备'] == device]
            if len(device_data_orig) > 0 and '分光器容量' in device_data_orig.columns:
                max_port = device_data_orig['分光器容量'].iloc[-1]
                if max_port <= 0:
                    max_port = 32
                print(f"DEBUG [generate_results] - 设备: {device}, 分光器容量: {max_port}")

                # 直接使用计算值作为预测结果
                calc_pred_ports = int(round(max_port * (1 - pred_occupancy)))
                print(f"DEBUG [generate_results] - 设备: {device}, 原预测空闲端口数: {pred_ports}, 修正为: {calc_pred_ports}")
                pred_ports = calc_pred_ports

        # 获取当前数据
        device_latest = latest_data[latest_data['所属设备'] == device]
        is_new_district = False

        if len(device_latest) > 0:
            # 获取当前值
            current_occupancy = device_latest['实占率'].iloc[0] if '实占率' in device_latest.columns else None
            current_ports = device_latest['空闲端口数'].iloc[0] if '空闲端口数' in device_latest.columns else None
            current_demand = device_latest['潜在需求比'].iloc[0] if '潜在需求比' in device_latest.columns else None

            # 检查是否为新小区
            if '是新小区' in device_latest.columns:
                is_new_district = device_latest['是新小区'].iloc[0]
        else:
            current_occupancy = None
            current_ports = None
            current_demand = None

        # 计算当前风险评分
        current_score = None
        current_level = None
        current_reasons = []

        if current_occupancy is not None and current_ports is not None and current_demand is not None:
            current_score, current_level, current_reasons = calculate_risk_score(
                current_occupancy, current_ports, current_demand, is_new_district
            )

        # 计算预测风险评分
        # 如果有ftth终端数和覆盖房间数，直接计算潜在需求比
        ftth_terminals = None
        covered_rooms = None

        if len(device_latest) > 0:
            if 'ftth终端数' in device_latest.columns:
                ftth_terminals = device_latest['ftth终端数'].iloc[0]
            if '覆盖的工程级的线路到达房间数' in device_latest.columns:
                covered_rooms = device_latest['覆盖的工程级的线路到达房间数'].iloc[0]

        # 如果有这两个值，直接计算潜在需求比
        calculated_demand = None
        if ftth_terminals is not None and covered_rooms is not None and covered_rooms > 0:
            calculated_demand = ftth_terminals / covered_rooms

        # 使用计算的潜在需求比或者原始的潜在需求比
        demand_to_use = calculated_demand if calculated_demand is not None else pred_demand

        pred_score, pred_level, pred_reasons = calculate_risk_score(
            pred_occupancy, pred_ports, demand_to_use, is_new_district
        )

        # 创建结果行
        result_row = {
            '设备': device,
            '数据变化类型': data_type
        }

        # 添加设备基本信息
        if len(device_latest) > 0:
            # 添加额外的设备信息字段
            for field in ['小区入库时间', '建筑群', 'address_desc', 'area', '覆盖的工程级的线路到达房间数', '分光器数', '分光器容量', 'ftth终端数']:
                if field in device_latest.columns:
                    result_row[field] = device_latest[field].iloc[0]

        # 添加当前值
        result_row['当前实占率百分比'] = f"{current_occupancy*100:.2f}%" if current_occupancy is not None else 'N/A'
        result_row['当前空闲端口数'] = current_ports
        result_row['当前潜在需求比百分比'] = f"{current_demand*100:.2f}%" if current_demand is not None else 'N/A'
        result_row['当前风险评分'] = current_score
        result_row['当前风险级别'] = current_level
        result_row['当前风险原因'] = ', '.join(current_reasons) if current_reasons else 'N/A'

        # 添加预测值
        result_row['预测实占率百分比'] = f"{pred_occupancy*100:.2f}%"
        result_row['预测空闲端口数'] = pred_ports
        print(f"DEBUG [最终结果 {device}] - 预测空闲端口数: {pred_ports}")
        result_row['预测潜在需求比百分比'] = f"{pred_demand*100:.2f}%"
        result_row['预测风险评分'] = pred_score
        result_row['预测风险级别'] = pred_level
        result_row['预测风险原因'] = ', '.join(pred_reasons)

        # 添加变化
        result_row['实占率变化'] = pred_occupancy - current_occupancy if current_occupancy is not None else None
        result_row['实占率变化百分比'] = f"{(pred_occupancy - current_occupancy)*100:.2f}%" if current_occupancy is not None else 'N/A'
        result_row['风险评分变化'] = pred_score - current_score if current_score is not None else None

        # 添加提醒
        result_row['提醒'] = pred_level

        output.append(result_row)

    # 处理没有预测结果的设备
    if missing_devices:
        print(f"正在处理 {len(missing_devices)} 个没有预测结果的设备...")

        # 获取最新一周的所有设备
        all_latest_devices = weekly_data.sort_values('周').groupby('所属设备').last().reset_index()

        for device in missing_devices:
            # 获取设备的最新数据
            device_latest = all_latest_devices[all_latest_devices['所属设备'] == device]

            if len(device_latest) == 0:
                print(f"设备 {device} 没有最新数据，跳过")
                continue  # 如果没有数据，跳过

            # 获取数据变化类型
            data_type = device_to_data_type.get(device, 'normal')  # 默认为normal类型

            # 创建结果行
            result_row = {
                '设备': device,
                '数据变化类型': data_type
            }

            # 添加设备基本信息
            for field in ['小区入库时间', '建筑群', 'address_desc', 'area', '覆盖的工程级的线路到达房间数', '分光器数', '分光器容量', 'ftth终端数']:
                if field in device_latest.columns:
                    result_row[field] = device_latest[field].iloc[0]

            # 获取当前值
            current_occupancy = device_latest['实占率'].iloc[0] if '实占率' in device_latest.columns else None
            current_ports = device_latest['空闲端口数'].iloc[0] if '空闲端口数' in device_latest.columns else None
            current_demand = device_latest['潜在需求比'].iloc[0] if '潜在需求比' in device_latest.columns else None

            # 检查是否为新小区
            is_new_district = False
            if '是新小区' in device_latest.columns:
                is_new_district = device_latest['是新小区'].iloc[0]
            elif '入库时间差_月' in device_latest.columns and device_latest['入库时间差_月'].iloc[0] <= 24:
                is_new_district = True

            # 计算当前风险评分
            current_score = None
            current_level = None
            current_reasons = []

            if current_occupancy is not None and current_ports is not None and current_demand is not None:
                current_score, current_level, current_reasons = calculate_risk_score(
                    current_occupancy, current_ports, current_demand, is_new_district
                )

            # 添加当前值
            result_row['当前实占率百分比'] = f"{current_occupancy*100:.2f}%" if current_occupancy is not None else 'N/A'
            result_row['当前空闲端口数'] = current_ports
            result_row['当前潜在需求比百分比'] = f"{current_demand*100:.2f}%" if current_demand is not None else 'N/A'
            result_row['当前风险评分'] = current_score
            result_row['当前风险级别'] = current_level
            result_row['当前风险原因'] = ', '.join(current_reasons) if current_reasons else 'N/A'

            # 对于数据不足的设备，使用最近一周的数据作为预测值
            result_row['预测实占率百分比'] = result_row['当前实占率百分比']
            result_row['预测空闲端口数'] = current_ports
            result_row['预测潜在需求比百分比'] = result_row['当前潜在需求比百分比']
            result_row['预测风险评分'] = current_score
            result_row['预测风险级别'] = current_level
            result_row['预测风险原因'] = '历史数据不足，保留原值'

            # 对于数据不足的设备，变化指标没有参考价值，设为特殊值
            result_row['实占率变化'] = None
            result_row['实占率变化百分比'] = '--'
            result_row['风险评分变化'] = None

            # 添加提醒
            result_row['提醒'] = f"{current_level}（数据不足）"

            output.append(result_row)

    return output

# 10. 可视化结果
def visualize_results(validation_results, histories, results_dir):
    """
    可视化验证结果和训练历史
    """
    # 创建结果目录
    os.makedirs(results_dir, exist_ok=True)

    # 创建验证结果数据框
    validation_df = pd.DataFrame(validation_results)

    # 1. 绘制预测值 vs 实际值散点图
    plt.figure(figsize=(15, 15))

    # 实占率预测
    plt.subplot(2, 2, 1)
    plt.scatter(validation_df['实际实占率'], validation_df['预测实占率'], alpha=0.5)
    plt.plot([0, 1], [0, 1], 'r--')
    plt.xlabel('实际实占率')
    plt.ylabel('预测实占率')
    plt.title('实占率预测效果')
    plt.grid(True)

    # 空闲端口数预测
    plt.subplot(2, 2, 2)
    plt.scatter(validation_df['实际空闲端口数'], validation_df['预测空闲端口数'], alpha=0.5)
    max_ports = max(validation_df['实际空闲端口数'].max(), validation_df['预测空闲端口数'].max())
    plt.plot([0, max_ports], [0, max_ports], 'r--')
    plt.xlabel('实际空闲端口数')
    plt.ylabel('预测空闲端口数')
    plt.title('空闲端口数预测效果')
    plt.grid(True)

    # 潜在需求比预测
    plt.subplot(2, 2, 3)
    plt.scatter(validation_df['实际潜在需求比'], validation_df['预测潜在需求比'], alpha=0.5)
    plt.plot([0, 1], [0, 1], 'r--')
    plt.xlabel('实际潜在需求比')
    plt.ylabel('预测潜在需求比')
    plt.title('潜在需求比预测效果')
    plt.grid(True)

    # 风险评分分布
    plt.subplot(2, 2, 4)
    if '预测风险评分' in validation_df.columns:
        plt.hist(validation_df['预测风险评分'], bins=20, alpha=0.5, label='预测风险评分')
    else:
        # 如果没有预测风险评分列，尝试使用其他列
        print("警告：找不到'预测风险评分'列，显示可用的列：", validation_df.columns.tolist())
        # 如果有其他风险评分相关的列，使用它
        risk_cols = [col for col in validation_df.columns if '风险' in col and '评分' in col]
        if risk_cols:
            plt.hist(validation_df[risk_cols[0]], bins=20, alpha=0.5, label=risk_cols[0])
            print(f"使用替代列：{risk_cols[0]}")

    plt.axvline(x=40, color='g', linestyle='--', label='注意阈值')
    plt.axvline(x=60, color='y', linestyle='--', label='警告阈值')
    plt.axvline(x=80, color='r', linestyle='--', label='紧急阈值')
    plt.xlabel('风险评分')
    plt.ylabel('设备数量')
    plt.title('风险评分分布')
    plt.legend()
    plt.grid(True)

    plt.tight_layout()
    plt.savefig(os.path.join(results_dir, '多指标预测效果对比.png'), dpi=300)
    plt.close()

    # 2. 绘制训练历史曲线
    for data_type, history in histories.items():
        plt.figure(figsize=(15, 10))

        # 绘制损失曲线
        plt.subplot(2, 2, 1)
        plt.plot(history['loss'], label='训练集')
        plt.plot(history['val_loss'], label='验证集')
        plt.title(f'{data_type}类型数据模型总体损失')
        plt.xlabel('迭代次数')
        plt.ylabel('损失')
        plt.legend()
        plt.grid(True)

        # 绘制实占率预测损失曲线
        plt.subplot(2, 2, 2)
        plt.plot(history['occupancy_output_loss'], label='训练集')
        plt.plot(history['val_occupancy_output_loss'], label='验证集')
        plt.title(f'{data_type}类型数据实占率预测损失')
        plt.xlabel('迭代次数')
        plt.ylabel('损失')
        plt.legend()
        plt.grid(True)

        # 绘制空闲端口数预测损失曲线
        plt.subplot(2, 2, 3)
        plt.plot(history['ports_output_loss'], label='训练集')
        plt.plot(history['val_ports_output_loss'], label='验证集')
        plt.title(f'{data_type}类型数据空闲端口数预测损失')
        plt.xlabel('迭代次数')
        plt.ylabel('损失')
        plt.legend()
        plt.grid(True)

        # 绘制潜在需求比预测损失曲线
        plt.subplot(2, 2, 4)
        plt.plot(history['demand_output_loss'], label='训练集')
        plt.plot(history['val_demand_output_loss'], label='验证集')
        plt.title(f'{data_type}类型数据潜在需求比预测损失')
        plt.xlabel('迭代次数')
        plt.ylabel('损失')
        plt.legend()
        plt.grid(True)

        plt.tight_layout()
        plt.savefig(os.path.join(results_dir, f'{data_type}类型数据训练历史.png'), dpi=300)
        plt.close()

# 11. 主函数
def main():
    # 设置路径
    data_folder = 'D:/OBD/data_processed'  # 使用与LSTM.py相同的数据文件夹路径
    results_dir = 'LSTM-R_results'
    os.makedirs(results_dir, exist_ok=True)

    # 检查数据文件夹是否存在
    if not os.path.exists(data_folder):
        print(f"错误：数据文件夹 {data_folder} 不存在")
        return

    # 检查数据文件夹中的文件
    files = os.listdir(data_folder)
    print(f"数据文件夹中的文件：{files}")

    # 设置随机种子，确保结果可重现
    np.random.seed(42)
    tf.random.set_seed(42)

    # 1. 数据加载与预处理
    print("加载数据...")
    all_data = load_and_preprocess_data(folder_path=data_folder)  # 显式指定参数名
    print(f"加载了 {len(all_data)} 条数据记录")

    # 2. 特征工程
    print("进行特征工程...")
    all_data = feature_engineering(all_data)

    # 3. 数据聚合
    print("按周聚合数据...")
    weekly_data = aggregate_weekly_data(all_data)
    print(f"共有 {len(weekly_data['所属设备'].unique())} 个设备的周度数据")

    # 输出数据变化类型统计
    data_type_counts = weekly_data['数据变化类型'].value_counts()
    print("数据变化类型统计:")
    for data_type, count in data_type_counts.items():
        print(f"  {data_type}: {count} 个设备")

    # 4. 数据分割
    print("分割训练集和测试集...")
    # 按设备分组，确保同一设备的数据不会同时出现在训练集和测试集中
    devices = weekly_data['所属设备'].unique()
    train_devices, test_devices = train_test_split(devices, test_size=0.2, random_state=42)

    train_data = weekly_data[weekly_data['所属设备'].isin(train_devices)]
    test_data = weekly_data[weekly_data['所属设备'].isin(test_devices)]

    print(f"训练集: {len(train_data)} 条记录, {len(train_devices)} 个设备")
    print(f"测试集: {len(test_data)} 条记录, {len(test_devices)} 个设备")

    # 5. 数据标准化
    print("数据标准化...")
    train_data, test_data, scalers = standardize_data(train_data, test_data)
    print("标准化完成")

    # 6. 训练模型与预测
    print("开始训练多指标LSTM模型...")
    predictions, validation_results, histories = train_and_predict(train_data, test_data, scalers, all_data)

    # 7. 生成预测结果和风险评分
    print("生成预测结果和风险评分...")

    # 记录没有预测的设备
    all_devices = set(weekly_data['所属设备'].unique())
    predicted_devices = set(predictions.keys())
    missing_devices = all_devices - predicted_devices
    print(f"有 {len(missing_devices)} 个设备没有预测结果")

    # 将所有设备传递给generate_results函数
    output = generate_results(predictions, validation_results, weekly_data, missing_devices, all_data)

    # 8. 保存结果
    print("保存结果...")
    # 使用时间戳生成唯一文件名，避免文件被占用
    timestamp = pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')
    output_file = os.path.join(results_dir, f'LSTM-R多指标预测结果_{timestamp}.xlsx')

    # 创建结果数据框并保存
    output_df = pd.DataFrame(output)
    output_df.to_excel(output_file, index=False)
    print(f'预测结果已保存至 {output_file}')

    # 9. 可视化结果
    print("生成可视化结果...")
    visualize_results(validation_results, histories, results_dir)
    print(f"可视化结果已保存至 {results_dir} 目录")

    # 10. 输出统计信息
    print(f"总预测设备数: {len(predictions)}")

    # 统计不同风险级别的设备数量
    risk_levels = output_df['预测风险级别'].value_counts()
    print("风险级别统计:")
    for level, count in risk_levels.items():
        print(f"  {level}: {count} 个设备")

    # 统计需要关注的设备
    high_risk_devices = output_df[output_df['预测风险级别'].isin(['紧急', '警告'])]
    print(f"需要关注的设备数(紧急+警告): {len(high_risk_devices)}")

    # 按数据变化类型统计风险级别
    type_risk = pd.crosstab(output_df['数据变化类型'], output_df['预测风险级别'])
    print("各类型数据风险级别统计:")
    print(type_risk)

    print("多指标预测与综合评分完成!")

if __name__ == "__main__":
    main()