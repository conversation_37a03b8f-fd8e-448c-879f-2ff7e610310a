import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler
from tensorflow.keras.models import Model
from tensorflow.keras.layers import Input, LSTM, Dense, Dropout
from tensorflow.keras.optimizers import <PERSON>
from tensorflow.keras.callbacks import EarlyStopping

# 测试数据加载
print("测试数据加载...")
data_folder = 'D:/OBD/data_processed'
file_path = os.path.join(data_folder, 'OBD_data.xlsx')

if os.path.exists(file_path):
    data = pd.read_excel(file_path)
    print(f"成功加载数据，共 {len(data)} 条记录")
    print(f"数据列: {data.columns.tolist()}")
else:
    print(f"找不到文件: {file_path}")
    # 尝试列出目录中的文件
    if os.path.exists(data_folder):
        files = os.listdir(data_folder)
        print(f"目录 {data_folder} 中的文件: {files}")
    else:
        print(f"目录 {data_folder} 不存在")

# 测试数据处理
print("\n测试数据处理...")
if 'data' in locals() and len(data) > 0:
    # 检查关键列是否存在
    key_columns = ['所属设备', '实占率', '空闲端口数', '潜在需求比']
    missing_columns = [col for col in key_columns if col not in data.columns]
    
    if missing_columns:
        print(f"缺少关键列: {missing_columns}")
    else:
        print("所有关键列都存在")
        
        # 检查数据类型
        for col in key_columns:
            print(f"列 {col} 的数据类型: {data[col].dtype}")
            
            # 检查是否有非数值数据
            if pd.api.types.is_numeric_dtype(data[col]):
                non_numeric = 0
            else:
                non_numeric = data[col].apply(lambda x: not pd.api.types.is_numeric(x)).sum()
            print(f"  非数值数据数量: {non_numeric}")
            
            # 检查是否有缺失值
            missing = data[col].isna().sum()
            print(f"  缺失值数量: {missing}")
            
            # 检查是否有无穷大值
            if pd.api.types.is_numeric_dtype(data[col]):
                inf_values = np.isinf(data[col].replace([np.inf, -np.inf], np.nan).dropna()).sum()
                print(f"  无穷大值数量: {inf_values}")

# 测试模型构建
print("\n测试模型构建...")
try:
    # 创建一个简单的LSTM模型
    seq_length = 4
    n_features = 10
    
    # 输入层
    input_layer = Input(shape=(seq_length, n_features))
    
    # LSTM层
    lstm_layer = LSTM(32, activation='relu')(input_layer)
    dropout_layer = Dropout(0.2)(lstm_layer)
    
    # 输出层
    output_layer = Dense(1, activation='sigmoid')(dropout_layer)
    
    # 创建模型
    model = Model(inputs=input_layer, outputs=output_layer)
    
    # 编译模型
    model.compile(optimizer=Adam(learning_rate=0.001), loss='mse', metrics=['mae'])
    
    print("模型构建成功")
    print(f"模型摘要:")
    model.summary()
except Exception as e:
    print(f"模型构建失败: {str(e)}")

print("\n测试完成")
